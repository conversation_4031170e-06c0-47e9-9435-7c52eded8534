/**
 * 地图ECharts渲染工具类
 * 负责处理地图图层的渲染、坐标转换和图形计算
 */

/**
 * 计算三次贝塞尔曲线上的点
 * @param {Array} p0 起点坐标 [x, y]
 * @param {Array} p1 控制点1坐标 [x, y]
 * @param {Array} p2 控制点2坐标 [x, y]
 * @param {Array} p3 终点坐标 [x, y]
 * @param {Number} numPoints 生成的点数量
 * @returns {Array} 贝塞尔曲线上的点数组
 */
export function calculateBezierPoints(p0, p1, p2, p3, numPoints) {
  const points = [];
  
  for (let i = 0; i <= numPoints; i++) {
    const t = i / numPoints;
    
    // 三次贝塞尔曲线公式: B(t) = (1-t)^3 * P0 + 3(1-t)^2 * t * P1 + 3(1-t) * t^2 * P2 + t^3 * P3
    const mt = 1 - t;
    const mt2 = mt * mt;
    const mt3 = mt2 * mt;
    const t2 = t * t;
    const t3 = t2 * t;
    
    const x = mt3 * p0[0] + 3 * mt2 * t * p1[0] + 3 * mt * t2 * p2[0] + t3 * p3[0];
    const y = mt3 * p0[1] + 3 * mt2 * t * p1[1] + 3 * mt * t2 * p2[1] + t3 * p3[1];
    
    points.push([x, y]);
  }
  
  return points;
}

/**
 * 计算圆弧上的点
 * @param {Array} start 起点坐标 [x, y]
 * @param {Array} end 终点坐标 [x, y]
 * @param {Number} radian 弧度值
 * @param {Boolean} reverse 是否反转方向
 * @returns {Array} 圆弧上的点数组
 */
export function calculateArcPoints(start, end, radian, reverse = false) {
  // 计算两点间距离
  const dx = end[0] - start[0];
  const dy = end[1] - start[1];
  const length = Math.sqrt(dx * dx + dy * dy);
  
  // 计算弦长与半径的关系
  // 弦长 = 2 * 半径 * sin(圆心角/2)
  // 因此 半径 = 弦长 / (2 * sin(圆心角/2))
  const radius = Math.abs(length / (2 * Math.sin(Math.abs(radian) / 2)));
  
  // 计算两点的中点
  const midX = (start[0] + end[0]) / 2;
  const midY = (start[1] + end[1]) / 2;
  
  // 计算从起点到终点的向量
  const vx = end[0] - start[0];
  const vy = end[1] - start[1];
  
  // 计算垂直于该向量的单位向量
  const perpLength = Math.sqrt(vx * vx + vy * vy);
  const perpX = -vy / perpLength;
  const perpY = vx / perpLength;
  
  // 计算圆心到中点的距离
  // 使用勾股定理：半径^2 = (圆心到中点距离)^2 + (弦长/2)^2
  // 因此 圆心到中点距离 = sqrt(半径^2 - (弦长/2)^2)
  const halfChord = length / 2;
  const centerToMidDist = Math.sqrt(radius * radius - halfChord * halfChord);
  
  // 确定圆心方向
  // 如果radian为正，圆心在向量左侧；如果为负，圆心在向量右侧
  let directionFactor = (radian > 0) ? 1 : -1;
  
  // 如果reverse为true，则反转方向
  if (reverse) {
    directionFactor *= -1;
  }
  
  // 计算圆心坐标
  const centerX = midX + perpX * centerToMidDist * directionFactor;
  const centerY = midY + perpY * centerToMidDist * directionFactor;
  
  // 计算起始角度和结束角度
  const startAngle = Math.atan2(start[1] - centerY, start[0] - centerX);
  let endAngle = Math.atan2(end[1] - centerY, end[0] - centerX);
  
  // 确保角度差是正确的弧度值
  // 计算角度差，确保方向正确
  let angleDiff = endAngle - startAngle;
  
  // 标准化角度差到 [-PI, PI] 范围
  if (angleDiff > Math.PI) angleDiff -= 2 * Math.PI;
  if (angleDiff < -Math.PI) angleDiff += 2 * Math.PI;
  
  // 如果角度差的符号与期望的弧度值符号不同，调整结束角度
  if ((angleDiff > 0 && radian < 0) || (angleDiff < 0 && radian > 0)) {
    if (endAngle > startAngle) {
      endAngle -= 2 * Math.PI;
    } else {
      endAngle += 2 * Math.PI;
    }
  }
  
  // 根据reverse参数确定扫描方向
  const sweepAngle = Math.abs(radian);
  let sweepDirection = (radian > 0) ? 1 : -1;
  if (reverse) {
    sweepDirection *= -1;
  }
  
  // 生成圆弧点
  const points = [];
  const steps = 100;
  for (let i = 0; i <= steps; i++) {
    const t = i / steps;
    const angle = startAngle + t * sweepAngle * sweepDirection;
    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);
    points.push([x, y]);
  }
  
  return points;
}

/**
 * 创建基础ECharts配置
 * @param {Number} containerWidth 容器宽度
 * @param {Number} containerHeight 容器高度
 * @returns {Object} ECharts基础配置
 */
export function createBaseEchartsOption(containerWidth, containerHeight) {
  return {
    backgroundColor: '#f5f7fa',
    animation: false,
    hoverLayerThreshold: Infinity,
    grid: {
      show: false,
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      containLabel: false
    },
    xAxis: {
      show: false,
      type: 'value',
      min: 0,
      max: containerWidth
    },
    yAxis: {
      show: false,
      type: 'value',
      min: 0,
      max: containerHeight
    },
    series: [],
    graphic: []
  };
}

/**
 * 标准pt到px的转换比例
 * 1pt = 1/72英寸，1px = 1/96英寸
 */
export const STANDARD_PT_TO_PX_RATIO = 96 / 72;
