<template>
	<div class="app-container">

		<el-form :model="queryParams" :inline="true" label-width="68px">
			<el-form-item label="日志内容">
				<el-input v-model="queryParams.content" placeholder="请输入日志内容" clearable></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handleQuery">搜索</el-button>
				<el-button @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>
		
		<el-row :gutter="10" class="mb8">
		  <el-col :span="1.5">
		    <el-button type="warning" plain @click="handleExport">导出</el-button>
		  </el-col>
		</el-row>

		<el-table v-loading="loading" :data="dataList">
			<el-table-column label="记录时间" align="center" prop="createTime">
				<template #default="scope">
					{{ parseTime(scope.row.createTime) }}
				</template>
			</el-table-column>
			<el-table-column label="机器人名称" align="center" prop="robotName" />
			<el-table-column label="类型" align="center" prop="logType" />
			<el-table-column label="日志内容" align="center" prop="content" />
		</el-table>

		<pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
			v-model:limit="queryParams.pageSize" @pagination="getList" />
	</div>
</template>

<script>
	import industrialControl from '@/api/log/industrialControl';

	export default {
		data() {
			return {
				proxy: getCurrentInstance().proxy,
				dataList: [],
				loading: false,
				total: 0,
				queryParams: {
					pageNum: 1,
					pageSize: 10
				}
			}
		},
		mounted() {
			this.getList()
		},
		methods: {
			getList() {
				this.loading = true
				industrialControl.getList(this.queryParams).then(res => {
					this.dataList = res.rows
					this.total = res.total
					this.loading = false
				})
			},			
			resetQuery() {
				this.queryParams = {
					pageNum: 1,
					pageSize: 10
				}
			},			
			handleQuery() {
				this.getList()
			},
			handleExport() {
				this.download("logIndustrialControl/export", {
				  ...this.queryParams
				},`industrialControl_${new Date().getTime()}.xlsx`)
			}
		}
	}
</script>

<style>

</style>