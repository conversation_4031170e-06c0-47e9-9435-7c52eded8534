import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/hyc";


// 查询识别类型下拉树结构
export function idenTypeTreeSelect() {
  return request({
    url: '/iden/type/getIdenTypeTree',
    method: 'get'
  })
}

// 查询识别类型详细
export function getIdenType(id) {
  return request({
    url: '/iden/type/getById/' + parseStrEmpty(id),
    method: 'get'
  })
}

// 新增识别类型
export function addIdenType(data) {
  return request({
    url: '/iden/type/add',
    method: 'post',
    data: data
  })
}

// 修改识别类型
export function updateIdenType(data) {
  return request({
    url: '/iden/type/update',
    method: 'put',
    data: data
  })
}

// 删除识别类型
export function deleteIdenType(id) {
  return request({
    url: '/iden/type/delete/' + id,
    method: 'delete'
  })
}

export function listAllIdenType() {
  return request({
    url: '/iden/type/getAll',
    method: 'get'
  })
}
