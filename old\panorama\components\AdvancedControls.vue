<template>
  <div class="advanced-controls">
    <div class="control-panel">
      <h4>XMAP图层精细控制</h4>

      <div class="control-group">
        <label>水平位置:</label>
        <el-input-number
          v-model="localXmapTransform.translateX"
          :step="1"
          size="mini"
          controls-position="right"
          @change="updateTransform"></el-input-number>
        <el-slider
          v-model="localXmapTransform.translateX"
          :min="-500"
          :max="500"
          :step="1"
          @change="updateTransform"></el-slider>
      </div>

      <div class="control-group">
        <label>垂直位置:</label>
        <el-input-number
          v-model="localXmapTransform.translateY"
          :step="1"
          size="mini"
          controls-position="right"
          @change="updateTransform"></el-input-number>
        <el-slider
          v-model="localXmapTransform.translateY"
          :min="-500"
          :max="500"
          :step="1"
          @change="updateTransform"></el-slider>
      </div>

      <div class="control-group">
        <label>步长设置:</label>
        <div class="step-controls">
          <div>
            <span>平移步长:</span>
            <el-input-number
              v-model="localTransformStep.translate"
              :step="0.1"
              :precision="1"
              :min="0.1"
              :max="10"
              size="mini"
              controls-position="right"
              @change="updateTransformStep"></el-input-number>
          </div>
        </div>
      </div>

      <div class="control-buttons">
        <el-button size="mini" @click="resetTransform">重置</el-button>
        <el-button size="mini" @click="$emit('close')">关闭</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AdvancedControls",
  props: {
    xmapTransform: {
      type: Object,
      required: true
    },
    transformStep: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      localXmapTransform: { ...this.xmapTransform },
      localTransformStep: { ...this.transformStep }
    };
  },
  watch: {
    xmapTransform: {
      handler(newVal) {
        this.localXmapTransform = { ...newVal };
      },
      deep: true
    },
    transformStep: {
      handler(newVal) {
        this.localTransformStep = { ...newVal };
      },
      deep: true
    }
  },
  methods: {
    updateTransform() {
      // 发送更新事件
      this.$emit('update:xmap-transform', { ...this.localXmapTransform });

      // 发送保存事件
      this.$emit('save-transform');
    },
    updateTransformStep() {
      this.$emit('update:transform-step', { ...this.localTransformStep });
    },
    resetTransform() {
      this.$emit('reset-transform');
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";
</style>

