
function wsshClient() {};

wsshClient.prototype._generateEndpoint = function() {
	if (window.location.protocol == 'https:') {
		var protocol = 'wss://';
	} else {
		var protocol = 'ws://';
	}	
	var endpoint = protocol + sessionStorage.getItem('serverIp') + ':8099/webssh';
	return endpoint;
};

wsshClient.prototype.connect = function(options) {
	var endpoint = this._generateEndpoint();
	if (window.WebSocket) {
		//如果支持websocket
		this._connection = new WebSocket(endpoint);
	} else {
		//否则报错
		options.onError('WebSocket Not Supported');
		return;
	}

	this._connection.onopen = function() {
		options.onConnect();
	};

	this._connection.onmessage = function(evt) {
		var data = evt.data.toString();
		//data = base64.decode(data);
		options.onData(data);
	};


	this._connection.onclose = function(evt) {
		options.onClose();
	};
};

wsshClient.prototype.send = function(data) {
	this._connection.send(JSON.stringify(data));
};

wsshClient.prototype.sendInitData = function(options) {
	//连接参数
	this._connection.send(JSON.stringify(options));
}

wsshClient.prototype.sendClientData = function(data) {
	//发送指令
	this._connection.send(JSON.stringify({
		"operate": "command",
		"command": data
	}))
}

const client = new wsshClient();

// module.exports = WSSHClient;

export default {
	client
}