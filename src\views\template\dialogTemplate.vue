<template>
	<div class="app-container">
		<el-dialog v-model="dialogVisible" :title="title" :close-on-press-escape="false" :show-close="false" :close-on-click-modal="false">
			<el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
				<el-row :gutter="15">
					<el-col :span="12">
						<el-form-item label="点位名称" prop="instanceName">
							<el-input v-model="form.instanceName" placeholder="请输入点位名称"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="所属部门" prop="deptName">
							<el-input v-model="form.deptName" type="text" placeholder="请输入所属部门"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="Toolgroup" prop="toolGroup">
							<el-input v-model="form.toolGroup" type="text" placeholder="请输入Toolgroup"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="Toolid" prop="toolId">
							<el-input v-model="form.toolId" type="text" placeholder="请输入Toolid"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="点位类型" prop="className">
							<el-input v-model="form.className" type="text" placeholder="请输入点位类型"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handelConfirm">确定</el-button>
			</template>
		</el-dialog>
	</div>
</template>
<script>
	export default {
		data() {
			return {
				title: '点位添加',
				formRef: null,
				form: {},
				rules: {
					instanceName: [{
						required: true,
						message: '请输入点位名称',
						trigger: 'blur'
					}],
					deptName: [{
						required: true,
						message: '请输入所属部门',
						trigger: 'blur'
					}],
					toolGroup: [{
						required: true,
						message: '请输入Toolgroup',
						trigger: 'blur'
					}],
					toolId: [{
						required: true,
						message: '请输入Toolid',
						trigger: 'blur'
					}],
					className: [{
						required: true,
						message: '请输入点位类型',
						trigger: 'blur'
					}],
				},
				dialogVisible: false
			}
		},
		methods: {
			init() {
				this.dialogVisible = true
			},
			handelConfirm() {
				this.$refs.formRef.validate((valid) => {
					if (!valid) return;


					this.dialogVisible = true
					this.$emit('confirm')
				})
			}
		}
	}
</script>