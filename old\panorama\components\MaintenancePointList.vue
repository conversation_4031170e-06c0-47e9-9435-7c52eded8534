<template>
  <div class="maintenance-point-list">
    <div class="list-header">
      <h3>检修点列表</h3>
      <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAddPoint">添加</el-button>
    </div>
    
    <div class="search-box">
      <el-input
        v-model="searchText"
        placeholder="搜索检修点"
        prefix-icon="el-icon-search"
        clearable
        size="small"
      ></el-input>
    </div>
    
    <div class="point-list" v-loading="loading">
      <el-empty description="暂无检修点" v-if="filteredPoints.length === 0"></el-empty>
      
      <el-card 
        v-for="point in filteredPoints" 
        :key="point.id"
        :class="{'point-card': true, 'selected': isSelected(point)}"
        @click.native="handleSelectPoint(point)"
      >
        <div class="point-info">
          <div class="point-name">{{ point.name || point.instanceName }}</div>
          <div class="point-type">{{ getPointTypeName(point.className) }}</div>
          <div class="point-coords">
            <span>X: {{ formatCoord(point.x) }}</span>
            <span>Y: {{ formatCoord(point.y) }}</span>
          </div>
        </div>
        <div class="point-actions">
          <el-button 
            type="primary" 
            icon="el-icon-edit" 
            size="mini" 
            circle
            @click.stop="handleEditPoint(point)"
          ></el-button>
          <el-button 
            type="danger" 
            icon="el-icon-delete" 
            size="mini" 
            circle
            @click.stop="handleDeletePoint(point)"
          ></el-button>
        </div>
      </el-card>
    </div>
    
    <!-- 编辑点位对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="pointForm" :rules="pointRules" ref="pointForm" label-width="100px">
        <el-form-item label="点位名称" prop="name">
          <el-input v-model="pointForm.name" placeholder="请输入点位名称"></el-input>
        </el-form-item>
        <el-form-item label="点位类型" prop="className">
          <el-select v-model="pointForm.className" placeholder="请选择点位类型">
            <el-option label="检修点" value="MaintenancePoint"></el-option>
            <el-option label="工作点" value="JobPoint"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="X坐标" prop="x">
          <el-input-number v-model="pointForm.x" :precision="2" :step="0.1"></el-input-number>
        </el-form-item>
        <el-form-item label="Y坐标" prop="y">
          <el-input-number v-model="pointForm.y" :precision="2" :step="0.1"></el-input-number>
        </el-form-item>
        <el-form-item label="允许旋转" prop="allowRevolve">
          <el-switch
            v-model="pointForm.allowRevolve"
            active-value="1"
            inactive-value="0"
          ></el-switch>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="pointForm.remark" 
            type="textarea" 
            placeholder="请输入备注信息"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPointForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'MaintenancePointList',
  props: {
    points: {
      type: Array,
      default: () => []
    },
    selectedPoint: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      searchText: '',
      dialogVisible: false,
      dialogTitle: '添加检修点',
      isEditing: false,
      pointForm: {
        id: null,
        name: '',
        className: 'MaintenancePoint',
        x: 0,
        y: 0,
        allowRevolve: '1',
        remark: ''
      },
      pointRules: {
        name: [
          { required: true, message: '请输入点位名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        className: [
          { required: true, message: '请选择点位类型', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    filteredPoints() {
      if (!this.searchText) {
        return this.points;
      }
      
      const searchLower = this.searchText.toLowerCase();
      return this.points.filter(point => {
        const name = (point.name || point.instanceName || '').toLowerCase();
        return name.includes(searchLower);
      });
    }
  },
  methods: {
    // 格式化坐标，保留2位小数
    formatCoord(value) {
      return value ? parseFloat(value).toFixed(2) : '0.00';
    },
    
    // 获取点位类型名称
    getPointTypeName(className) {
      const typeMap = {
        'MaintenancePoint': '检修点',
        'JobPoint': '工作点',
        'LandMark': '路径点',
        'ChargePoint': '充电点',
        'HomePoint': '充电房',
        'ReturnPoint': '返航点'
      };
      return typeMap[className] || className || '未知类型';
    },
    
    // 判断点位是否被选中
    isSelected(point) {
      return this.selectedPoint && this.selectedPoint.id === point.id;
    },
    
    // 处理选择点位
    handleSelectPoint(point) {
      this.$emit('select-point', point);
    },
    
    // 处理添加点位
    handleAddPoint() {
      this.isEditing = false;
      this.dialogTitle = '添加检修点';
      this.pointForm = {
        id: null,
        name: `检修点${this.points.length + 1}`,
        className: 'MaintenancePoint',
        x: 0,
        y: 0,
        allowRevolve: '1',
        remark: ''
      };
      this.dialogVisible = true;
      
      // 重置表单验证
      this.$nextTick(() => {
        if (this.$refs.pointForm) {
          this.$refs.pointForm.resetFields();
        }
      });
    },
    
    // 处理编辑点位
    handleEditPoint(point) {
      this.isEditing = true;
      this.dialogTitle = '编辑检修点';
      this.pointForm = { ...point };
      this.dialogVisible = true;
      
      // 重置表单验证
      this.$nextTick(() => {
        if (this.$refs.pointForm) {
          this.$refs.pointForm.resetFields();
        }
      });
    },
    
    // 处理删除点位
    handleDeletePoint(point) {
      this.$confirm(`确认删除检修点 "${point.name || point.instanceName}" 吗？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete-point', point.id);
        this.$message.success('删除成功');
      }).catch(() => {});
    },
    
    // 提交点位表单
    submitPointForm() {
      this.$refs.pointForm.validate(valid => {
        if (valid) {
          if (this.isEditing) {
            // 更新点位
            this.$emit('update-point', { ...this.pointForm });
            this.$message.success('更新成功');
          } else {
            // 添加点位
            const newPoint = { 
              ...this.pointForm,
              id: Date.now(), // 临时ID
              instanceName: this.pointForm.name
            };
            this.$emit('add-point', newPoint);
            this.$message.success('添加成功');
          }
          this.dialogVisible = false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.maintenance-point-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
  
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .search-box {
    margin-bottom: 10px;
  }
  
  .point-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
    
    .point-card {
      margin-bottom: 10px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
      
      &.selected {
        border: 1px solid #409EFF;
        background-color: #ecf5ff;
      }
      
      ::v-deep .el-card__body {
        padding: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .point-info {
        flex: 1;
        
        .point-name {
          font-weight: 500;
          font-size: 14px;
          margin-bottom: 5px;
        }
        
        .point-type {
          font-size: 12px;
          color: #909399;
          margin-bottom: 5px;
        }
        
        .point-coords {
          font-size: 12px;
          color: #606266;
          
          span {
            margin-right: 10px;
          }
        }
      }
      
      .point-actions {
        display: flex;
        gap: 5px;
      }
    }
  }
}
</style>
