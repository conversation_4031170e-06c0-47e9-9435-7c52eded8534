<template>
  <div class="map-layer-renderer">
    <div ref="echartsContainer" class="echarts-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import CoordinateTransformer from '../utils/CoordinateTransformer';
import {
  calculateBezierPoints,
  calculateArcPoints,
  createBaseEchartsOption
} from '../utils/MapEchartsRenderer';

export default {
  name: "MapLayerRenderer",
  props: {
    // 当前地图数据
    currentMap: {
      type: Object,
      default: null
    },
    // CAD图层URL
    cadLayerUrl: {
      type: String,
      default: ""
    },
    // XMAP图层URL
    xmapLayerUrl: {
      type: String,
      default: ""
    },
    // 当前激活的图层
    activeLayer: {
      type: String,
      default: "center"
    },
    // CAD图层变换属性
    cadTransform: {
      type: Object,
      required: true
    },
    // XMAP图层变换属性
    xmapTransform: {
      type: Object,
      required: true
    },
    // 是否显示工作点位
    showWorkPoints: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      echartsInstance: null,
      transformer: new CoordinateTransformer(),
      // 像素单位的尺寸变量
      cadWidthPx: 0,
      cadHeightPx: 0,
      // pt单位的尺寸变量
      cadWidth: 0,
      cadHeight: 0,
      xmapWidth: 0,
      xmapHeight: 0,
      // pt单位的偏移量
      ptOffsetX: 0,
      ptOffsetY: 0,
      // 是否已初始化
      initialized: false
    };
  },
  watch: {
    // 监听图层URL变化，重新初始化
    cadLayerUrl() {
      this.initEcharts();
    },
    xmapLayerUrl() {
      this.initEcharts();
    },
    // 监听变换属性变化，更新图表
    cadTransform: {
      handler() {
        this.updateEchartsLayers();
      },
      deep: true
    },
    xmapTransform: {
      handler() {
        this.updateEchartsLayers();
      },
      deep: true
    },
    // 监听activeLayer变化，更新图表
    activeLayer() {
      this.updateEchartsLayers();
    },
    // 监听showWorkPoints变化，更新图表
    showWorkPoints() {
      this.updateEchartsLayers();
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initEcharts();
    });
    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', this.resizeEcharts);
  },
  beforeDestroy() {
    // 移除事件监听和销毁图表实例
    window.removeEventListener('resize', this.resizeEcharts);
    if (this.echartsInstance) {
      this.echartsInstance.dispose();
    }
  },
  methods: {
    // 初始化ECharts实例
    initEcharts() {
      // 确保DOM已经渲染
      this.$nextTick(() => {
        // 如果已经有实例，先销毁
        if (this.echartsInstance) {
          this.echartsInstance.dispose();
        }

        // 如果没有图层，不初始化
        if (!this.cadLayerUrl && !this.xmapLayerUrl) {
          return;
        }

        // 初始化ECharts实例
        this.echartsInstance = echarts.init(this.$refs.echartsContainer);

        // 预加载图片以获取尺寸
        this.preloadImages().then(() => {
          // 设置图表选项
          this.updateEchartsLayers();

          // 监听图表事件
          this.echartsInstance.on('click', this.handleChartClick);

          this.initialized = true;
        });
      });
    },

    // 预加载图片以获取尺寸
    preloadImages() {
      const promises = [];

      // 预加载CAD图层
      if (this.cadLayerUrl) {
        const cadPromise = new Promise((resolve) => {
          const img = new Image();
          img.onload = () => {
            // 保存图片像素尺寸
            this.cadWidthPx = img.width;
            this.cadHeightPx = img.height;

            // 获取后端提供的pt单位尺寸
            const cadWidthPt = this.currentMap && this.currentMap.cadWidth;
            const cadHeightPt = this.currentMap && this.currentMap.cadHeight;

            // 计算pt到px的转换比例
            if (cadWidthPt && cadHeightPt) {
              // 使用坐标转换器计算比例
              this.transformer.calculatePt2PxRatio(
                this.cadWidthPx,
                this.cadHeightPx,
                cadWidthPt,
                cadHeightPt
              );

              // 保存pt单位尺寸
              this.cadWidth = cadWidthPt;
              this.cadHeight = cadHeightPt;
            } else {
              // 使用标准转换比例
              this.transformer.setPt2PxRatio(CoordinateTransformer.STANDARD_PT_TO_PX_RATIO);

              // 从像素尺寸转换到pt
              this.cadWidth = this.transformer.pxToPt(this.cadWidthPx);
              this.cadHeight = this.transformer.pxToPt(this.cadHeightPx);
            }

            resolve();
          };
          img.onerror = () => {
            console.error('CAD图层加载失败');
            resolve();
          };
          img.src = this.cadLayerUrl;
        });
        promises.push(cadPromise);
      }

      // 预加载XMAP图层
      if (this.xmapLayerUrl) {
        const xmapPromise = new Promise((resolve) => {
          // 获取XMAP的pt单位尺寸
          const xmapWidthPt = this.currentMap && this.currentMap.xmapSvgWidth;
          const xmapHeightPt = this.currentMap && this.currentMap.xmapSvgHeight;

          // 保存XMAP的pt单位尺寸
          this.xmapWidth = xmapWidthPt;
          this.xmapHeight = xmapHeightPt;

          resolve();
        });
        promises.push(xmapPromise);
      }

      return Promise.all(promises);
    },

    // 更新ECharts图层
    updateEchartsLayers() {
      if (!this.echartsInstance) return;

      // 获取容器尺寸
      const containerWidth = this.$refs.echartsContainer.clientWidth;
      const containerHeight = this.$refs.echartsContainer.clientHeight;

      // 使用坐标转换器计算CAD图层位置
      const cadPosition = this.transformer.calculateLayerPosition(
        containerWidth,
        containerHeight,
        this.cadWidthPx,
        this.cadHeightPx
      );

      // 创建基础ECharts配置
      const option = createBaseEchartsOption(containerWidth, containerHeight);

      // 添加CAD图层
      if (this.cadLayerUrl) {
        const cadOpacity = this.activeLayer === 'cad' || this.activeLayer === 'center' ? 1 : 0.4;

        // 使用计算得到的位置和缩放
        const centeredX = cadPosition.x;
        const centeredY = cadPosition.y;
        const cadScale = cadPosition.scale;

        // 使用cadScale作为基础缩放，cadTransform.scale作为用户调整的缩放
        const finalScale = cadScale * this.cadTransform.scale;

        option.graphic.push({
          type: 'image',
          id: 'cadLayer',
          style: {
            image: this.cadLayerUrl,
            opacity: cadOpacity,
            width: this.cadWidthPx,
            height: this.cadHeightPx
          },
          position: [
            centeredX + this.cadTransform.translateX,
            centeredY + this.cadTransform.translateY
          ],
          rotation: this.cadTransform.rotation * Math.PI / 180,
          scale: [finalScale, finalScale],
          origin: [0, 0],
          z: 1,
          draggable: false
        });

        // 更新坐标转换器的基础变换
        this.transformer.setBaseTransform(cadScale, centeredX, centeredY);
      }

      // 添加XMAP图层
      if (this.xmapLayerUrl && this.xmapWidth && this.xmapHeight && this.activeLayer !== 'cad') {
        const xmapOpacity = this.activeLayer === 'xmap' || this.activeLayer === 'center' ? 1 : 0.4;

        // 将XMAP的pt单位尺寸转换为px单位
        const xmapWidthPx = this.transformer.ptToPx(this.xmapWidth);
        const xmapHeightPx = this.transformer.ptToPx(this.xmapHeight);

        // 计算pt单位的偏移量 - 将px单位转换为pt单位
        this.ptOffsetX = this.transformer.pxToPt(this.xmapTransform.translateX);
        this.ptOffsetY = this.transformer.pxToPt(this.xmapTransform.translateY);

        option.graphic.push({
          type: 'image',
          id: 'xmapLayer',
          style: {
            image: this.xmapLayerUrl,
            opacity: xmapOpacity,
            width: xmapWidthPx,
            height: xmapHeightPx
          },
          position: [
            this.transformer.baseX + this.xmapTransform.translateX,
            this.transformer.baseY + this.xmapTransform.translateY
          ],
          rotation: this.xmapTransform.rotation * Math.PI / 180,
          scale: [
            this.transformer.baseScale * this.xmapTransform.scale,
            this.transformer.baseScale * this.xmapTransform.scale
          ],
          origin: [0, 0],
          z: 2,
          draggable: false
        });
      }

      // 渲染工作点位和路径
      this.renderWorkPoints(option, containerHeight);
      this.renderRoutes(option, containerHeight);

      // 设置图表选项
      this.echartsInstance.setOption(option, true);
    },

    // 渲染工作点位
    renderWorkPoints(option, containerHeight) {
      // 添加工作点位 - 只在showWorkPoints为true时添加
      if (this.showWorkPoints && this.currentMap && this.currentMap.advancedPoints && this.currentMap.advancedPoints.length > 0) {
        // 创建散点图系列用于显示点位
        const pointData = [];

        // 处理每个点位
        this.currentMap.advancedPoints.forEach((point, index) => {
          // 使用坐标转换器计算点位位置
          const [finalX, finalY] = this.transformer.calculatePointPosition(
            point,
            this.cadTransform,
            containerHeight
          );

          pointData.push([finalX, finalY, point.name || `点位${index + 1}`]);
        });

        // 添加散点图系列
        option.series.push({
          type: 'scatter',
          id: 'workPoints',
          data: pointData,
          symbolSize: 20, // 点的大小
          symbol: 'pin', // 使用图钉形状，更加明显
          itemStyle: {
            color: '#FF5722', // 点的颜色
            borderColor: '#FFFFFF', // 边框颜色
            borderWidth: 2, // 边框宽度
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          },
          label: {
            show: true,
            position: 'top',
            distance: 5,
            formatter: '{@[2]}', // 显示点位名称
            backgroundColor: '#333',
            padding: [3, 5],
            borderRadius: 3,
            color: '#fff'
          },
          zlevel: 10 // 确保点位显示在最上层
        });
      }
    },

    // 渲染路径
    renderRoutes(option, containerHeight) {
      // 处理路径信息
      if (this.currentMap && this.currentMap.advancedRoutes && this.currentMap.advancedRoutes.length > 0) {
        // 遍历所有路径
        this.currentMap.advancedRoutes.forEach((route, _) => {
          // 使用坐标转换器计算路径点位置
          const startPos = this.transformer.calculatePointPosition(
            route.startPos,
            this.cadTransform,
            containerHeight
          );
          const endPos = this.transformer.calculatePointPosition(
            route.endPos,
            this.cadTransform,
            containerHeight
          );
          const ctrlPos1 = this.transformer.calculatePointPosition(
            route.ctrlPos1,
            this.cadTransform,
            containerHeight
          );
          const ctrlPos2 = this.transformer.calculatePointPosition(
            route.ctrlPos2,
            this.cadTransform,
            containerHeight
          );

          // 根据路径类型绘制不同的路径
          let graphicItem = null;

          switch (route.type) {
            case 'straight_line': // 直线
              graphicItem = {
                type: 'line',
                coordinateSystem: 'cartesian2d',
                data: [startPos, endPos],
                lineStyle: {
                  stroke: '#4CAF50',
                  width: 3
                },
                z: 5
              };
              option.series.push(graphicItem);
              break;

            case 'bezier_curve': // 贝塞尔曲线
              // 使用线段系列模拟贝塞尔曲线
              const bezierPoints = calculateBezierPoints(
                startPos,
                ctrlPos1,
                ctrlPos2,
                endPos,
                100 // 生成100个点以获得平滑的曲线
              );

              graphicItem = {
                type: 'line',
                coordinateSystem: 'cartesian2d',
                polyline: true,
                data: [{
                  coords: bezierPoints,
                  lineStyle: {
                    width: 3,
                    color: '#2196F3'
                  }
                }],
                effect: { show: false },
                lineStyle: {
                  opacity: 0.8,
                  width: 3,
                  color: '#2196F3'
                },
                z: 5
              };

              option.series.push(graphicItem);
              break;

            case 'convex': // 凸弧线
            case 'concave_arc': // 凹弧线
              // 使用圆弧
              const arcPoints = calculateArcPoints(startPos, endPos, route.radian, true);

              graphicItem = {
                type: 'lines',
                coordinateSystem: 'cartesian2d',
                polyline: true,
                data: [{
                  coords: arcPoints,
                  lineStyle: {
                    width: 3,
                    color: '#4CAF50'
                  }
                }],
                effect: { show: false },
                lineStyle: {
                  opacity: 0.8,
                  width: 1,
                },
                z: 5
              };

              option.series.push(graphicItem);
              break;

            default:
              console.warn(`未知的路径类型: ${route.type}`);
              break;
          }
        });
      }
    },

    // 调整图表大小
    resizeEcharts() {
      if (this.echartsInstance) {
        this.echartsInstance.resize();
        this.updateEchartsLayers();
      }
    },

    // 处理图表点击事件
    handleChartClick(params) {
      this.$emit('chart-click', params);
    },

    // 获取当前XMAP图层的pt单位偏移量
    getPtOffset() {
      return {
        x: this.ptOffsetX,
        y: this.ptOffsetY
      };
    },

    // 应用pt单位的偏移量
    applyPtOffset(ptOffsetX, ptOffsetY) {
      // 将pt单位转换为px单位
      const pxOffsetX = this.transformer.ptToPx(ptOffsetX);
      const pxOffsetY = this.transformer.ptToPx(ptOffsetY);

      // 更新XMAP图层变换属性
      this.$emit('update:xmap-transform', {
        ...this.xmapTransform,
        translateX: pxOffsetX,
        translateY: pxOffsetY
      });

      // 更新ECharts图层
      this.updateEchartsLayers();
    },

    // 更新地图元素
    updateMapElements() {
      if (this.initialized) {
        this.updateEchartsLayers();
      } else {
        this.initEcharts();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";
</style>
