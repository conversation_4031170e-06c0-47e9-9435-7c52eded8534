import request from '@/utils/request'

const add = (params) =>{
	return request.post('confRobotVoice/add', params)
}

const remove = (ids) =>{
	return request.get('confRobotVoice/remove/' + ids)
}

const edit = (params) =>{
	return request.post('confRobotVoice/edit', params)
}

const getList = (params) =>{
	return request.get('confRobotVoice/getList', {params: params})
}

const getAll = (params) =>{
	return request.get('confRobotVoice/getAll', {params: params})
}

const getOne = (id) =>{
	return request.get('confRobotVoice/getOne/' + id)
}

const issue = (params) =>{
	return request.post('confRobotVoice/issue', params)
}

export default {
	add, remove, edit, getList, getAll, getOne, issue
}