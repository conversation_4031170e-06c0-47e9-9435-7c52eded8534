<template>
  <div class="center-panel">
    <div class="cad-container">
      <!-- 没有CAD图层时显示空状态 -->
      <div class="placeholder-cad" v-if="!cadLayerUrl">
        <el-empty description="请先上传CAD图层">
          <template #image>
            <el-icon size="64" color="var(--el-color-info)">
              <Document />
            </el-icon>
          </template>
        </el-empty>
      </div>
      
      <!-- 有CAD图层时显示内容 -->
      <div class="cad-content" v-else>
        <!-- 工具栏 -->
        <div class="cad-toolbar">
          <div class="toolbar-left">
            <span class="toolbar-title">CAD图层</span>
          </div>
          <div class="toolbar-right">
            <el-button-group size="small">
              <el-button @click="resetTransform">
                <el-icon><Refresh /></el-icon>
                重置变换
              </el-button>
              <el-button @click="fitToContainer">
                <el-icon><FullScreen /></el-icon>
                适应窗口
              </el-button>
            </el-button-group>
          </div>
        </div>
        
        <!-- 变换控制面板 -->
        <div class="transform-controls">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="control-group">
                <label>旋转角度 (°):</label>
                <el-input-number
                  v-model="localTransform.rotation"
                  :precision="1"
                  :step="1"
                  size="small"
                  @change="updateTransform"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="control-group">
                <label>缩放比例:</label>
                <el-input-number
                  v-model="localTransform.scale"
                  :precision="2"
                  :step="0.1"
                  :min="0.1"
                  :max="5"
                  size="small"
                  @change="updateTransform"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="control-group">
                <label>X轴偏移 (px):</label>
                <el-input-number
                  v-model="localTransform.translateX"
                  :precision="0"
                  :step="10"
                  size="small"
                  @change="updateTransform"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="control-group">
                <label>Y轴偏移 (px):</label>
                <el-input-number
                  v-model="localTransform.translateY"
                  :precision="0"
                  :step="10"
                  size="small"
                  @change="updateTransform"
                />
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- CAD图层显示区域 -->
        <div class="cad-display" ref="cadDisplay">
          <div 
            class="cad-image-container"
            :style="transformStyle"
          >
            <img 
              :src="cadLayerUrl" 
              class="cad-image"
              @load="onImageLoad"
              @error="onImageError"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="CenterPanel">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Refresh, FullScreen } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  activeLayer: {
    type: String,
    default: "cad"
  },
  currentMap: {
    type: Object,
    default: null
  },
  cadLayerUrl: {
    type: String,
    default: ""
  },
  cadTransform: {
    type: Object,
    required: true
  }
})

// Refs
const cadDisplay = ref(null)

// Data
const localTransform = reactive({
  rotation: 0,
  scale: 1,
  translateX: 0,
  translateY: 0
})

const imageLoaded = ref(false)
const imageSize = reactive({
  width: 0,
  height: 0
})

// Computed
const transformStyle = computed(() => {
  return {
    transform: `translate(${localTransform.translateX}px, ${localTransform.translateY}px) 
                scale(${localTransform.scale}) 
                rotate(${localTransform.rotation}deg)`,
    transformOrigin: 'center center'
  }
})

// Watchers
watch(() => props.cadTransform, (newTransform) => {
  Object.assign(localTransform, newTransform)
}, { immediate: true, deep: true })

watch(() => props.cadLayerUrl, () => {
  imageLoaded.value = false
})

// Lifecycle
onMounted(() => {
  console.log('CenterPanel mounted')
})

// Methods
const updateTransform = () => {
  // 这里可以添加实时更新变换的逻辑
  console.log('Transform updated:', localTransform)
}

const resetTransform = () => {
  localTransform.rotation = 0
  localTransform.scale = 1
  localTransform.translateX = 0
  localTransform.translateY = 0
  updateTransform()
  ElMessage.success('变换已重置')
}

const fitToContainer = () => {
  if (!imageLoaded.value || !cadDisplay.value) {
    ElMessage.warning('图片未加载完成')
    return
  }
  
  const containerWidth = cadDisplay.value.clientWidth
  const containerHeight = cadDisplay.value.clientHeight
  
  if (imageSize.width && imageSize.height) {
    const scaleX = containerWidth / imageSize.width
    const scaleY = containerHeight / imageSize.height
    const scale = Math.min(scaleX, scaleY) * 0.9 // 留一些边距
    
    localTransform.scale = Math.max(0.1, Math.min(5, scale))
    localTransform.translateX = 0
    localTransform.translateY = 0
    updateTransform()
    
    ElMessage.success('已适应窗口大小')
  }
}

const onImageLoad = (event) => {
  imageLoaded.value = true
  imageSize.width = event.target.naturalWidth
  imageSize.height = event.target.naturalHeight
  
  // 自动适应窗口
  setTimeout(() => {
    fitToContainer()
  }, 100)
}

const onImageError = () => {
  imageLoaded.value = false
  ElMessage.error('CAD图层加载失败')
}

const updateMapElements = () => {
  console.log('CenterPanel updateMapElements called')
  // 可以在这里添加更新地图元素的逻辑
}

// 暴露方法给父组件
defineExpose({
  updateMapElements
})
</script>

<style lang="scss" scoped>
.center-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.cad-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.placeholder-cad {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cad-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.cad-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  
  .toolbar-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.transform-controls {
  padding: 16px;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-light);
  
  .control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    label {
      font-size: 12px;
      color: var(--el-text-color-regular);
      font-weight: 500;
    }
    
    .el-input-number {
      width: 100%;
    }
  }
}

.cad-display {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .cad-image-container {
    position: relative;
    transition: transform 0.3s ease;
    
    .cad-image {
      max-width: none;
      max-height: none;
      display: block;
      user-select: none;
      pointer-events: none;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .transform-controls {
    .el-col {
      margin-bottom: 12px;
    }
  }
}

@media (max-width: 768px) {
  .cad-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    
    .toolbar-left,
    .toolbar-right {
      text-align: center;
    }
  }
  
  .transform-controls {
    .control-group {
      margin-bottom: 16px;
    }
  }
}
</style>
