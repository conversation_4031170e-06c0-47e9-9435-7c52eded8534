<template>
  <el-dialog title="新建地图" :visible.sync="visible" width="500px" @close="handleClose">
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">
      <el-form-item label="地图名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入地图名称"></el-input>
      </el-form-item>
      <el-form-item label="地图缩放比例" prop="scale">
        <el-input
          v-model="form.scale"
          placeholder="请输入地图缩放比例">
        </el-input>
        <div class="el-upload__tip">请输入大于0且小于1的数字，例如：0.05</div>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          type="textarea"
          v-model="form.remark"
          placeholder="请输入备注信息"
          :rows="3">
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "CreateMapDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    initialForm: {
      type: Object,
      default: () => ({
        name: '',
        scale: '0.05',
        remark: ''
      })
    },
    rules: {
      type: Object,
      default: () => ({
        name: [
          { required: true, message: '请输入地图名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        scale: [
          { required: true, message: '请输入地图缩放比例', trigger: 'blur' },
          { pattern: /^0*\.0*[1-9][0-9]*$/, message: '请输入大于0且小于1的数字', trigger: 'blur' }
        ]
      })
    }
  },
  data() {
    return {
      form: { ...this.initialForm }
    };
  },
  watch: {
    initialForm: {
      handler(newVal) {
        this.form = { ...newVal };
      },
      deep: true
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('submit', { ...this.form });
        }
      });
    },
    handleCancel() {
      this.$emit('cancel');
    },
    handleClose() {
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";
</style>

