<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes :horizontal="appStore.device === 'mobile'" class="default-theme">
        <!--识别类型数据-->
        <pane size="16">
          <el-col>
            <div class="head-container">
              <el-input v-model="idenTypeName" placeholder="请输入识别类型名称" clearable prefix-icon="Search" style="margin-bottom: 20px" />
            </div>
            <div class="head-container">
              <el-tree :data="idenTypeOptions" 
              :props="{ label: 'idenTypeName', children: 'children' }" 
              :expand-on-click-node="false" 
              :filter-node-method="filterNode" ref="idenTypeTreeRef" node-key="id" highlight-current default-expand-all @node-click="handleNodeClick"  @node-contextmenu="floderOption"/>
            </div>
          </el-col>
        </pane>
        <pane size="14" >

          <div class="demo-image__error">

              <div class="block">
                <el-image v-if="currentIdenTypeId != 0" 
          :src="url + currentImageUrl" 
          :preview-src-list="currentImageUrl!=null && currentImageUrl!='' ? [url+currentImageUrl] : []">
                  <template #error>
                    <div class="image-slot">
                      <el-icon><icon-picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </div>

        </pane>
        <!--识别类型参数数据-->
        <pane size="70">
          <el-col v-if="currentIdenTypeId != 0">
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
              <el-form-item label="参数名称" prop="paramName">
                <el-input v-model="queryParams.paramName" placeholder="请输入参数名称" clearable style="width: 240px" @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
              </el-col>
              <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="paramList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column label="参数名称" align="center" key="paramName" prop="paramName"  v-if="columns[0].visible"/>
              <el-table-column label="数据类型" align="center" key="dataType" prop="dataType"  v-if="columns[1].visible">
                <template #default="scope">
                  <dict-tag :options="data_type" :value="scope.row.dataType" />
                </template>
              </el-table-column>
              <el-table-column label="取值范围" align="center" key="valueScopeJson" prop="valueScopeJson" v-if="columns[2].visible"/>
              <el-table-column label="状态" align="center" key="status" v-if="columns[3].visible">
                <template #default="scope">
                  <el-switch
                    v-model="scope.row.status"
                    active-value="0"
                    inactive-value="1"
                    @change="handleStatusChange(scope.row)"
                  ></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
                <template #default="scope">
                  <el-tooltip content="修改" placement="top" v-if="scope.row.userId !== 1">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
                  </el-tooltip>
                  <el-tooltip content="删除" placement="top" v-if="scope.row.userId !== 1">
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form :model="form" :rules="rules" ref="paramRef" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="参数名称" prop="paramName">
              <el-input v-model="form.paramName" placeholder="请输入参数名称" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="数据类型" prop="dataType">
              <el-select v-model="form.dataType" placeholder="请选择数据类型" clearable>
                <el-option v-for="dict in data_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="form.dataType == '2'">
          <el-col :span="24">
            <el-form-item label="取值范围">
              <el-input v-model="form.enumValue" placeholder="枚举类型，按逗号分隔方式填写，例如：红,橙,黄" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="form.dataType == '1'">
          <el-col :span="24">
            <el-form-item label="取值范围">
              <el-col :span="11">
                <el-input v-model="form.minValue" placeholder="最小值" />
              </el-col>
              <el-col :span="2" class="text-center">
                <span class="text-gray-500">-</span>
              </el-col>
              <el-col :span="11">
                <el-input v-model="form.maxValue" placeholder="最大值" />
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 添加或修改识别类型配置对话框 -->
    <el-dialog :title="idenTypeTitle" v-model="idenTypeOpen" width="600px" append-to-body>
      <el-form :model="idenTypeForm"  label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="识别类型名称" prop="paramName">
              <el-input v-model="idenTypeForm.idenTypeName" placeholder="请输入识别类型名称" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="是否标记起始点" prop="dataType">
              <el-select v-model="idenTypeForm.haveMinMax" placeholder="请选择是否标记起始点" clearable>
                <el-option label="否" value="0"></el-option>
                <el-option label="是" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="识别类型图标">
              <image-upload v-model="idenTypeForm.samplePhotoUrl" limit="1"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="idenTypeForm.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitIdenTypeForm">确 定</el-button>
          <el-button @click="idenTypeCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


    <div :style="{'z-index': 9999, position: 'fixed',left: optionCardX + 'px',
            top: optionCardY + 'px', width: '100px', background: 'white',
            'box-shadow': '0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)'}" v-show="optionCardShow"
      id="option-button-group">
      <el-button @click="handleCreateIdenType" icon="el-icon-plus">创建识别类型
      </el-button>
      <el-button @click="closeOptionCard" icon="el-icon-close">关闭当前窗口
      </el-button>
    </div>

    <div :style="{'z-index': 9999, position: 'fixed',left: optionCardX + 'px',
            top: optionCardY + 'px', width: '100px', background: 'white',
            'box-shadow': '0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)'}" v-show="idenTypeCardShow"
      id="option-button-group1">
      <el-button @click="handleUpdateIdenType" icon="el-icon-edit">编辑识别类型
      </el-button>
      <el-button @click="handleDeleteIdenType" icon="el-icon-delete">删除识别类型
      </el-button>
      <el-button @click="closeOptionCard" icon="el-icon-close">关闭当前窗口
      </el-button>
    </div>

  </div>
</template>

<script setup name="IdenType">
import { getToken } from "@/utils/auth"
import useAppStore from '@/store/modules/app'
import { idenTypeTreeSelect, getIdenType, addIdenType, updateIdenType, deleteIdenType } from "@/api/operation/idenType"
import { listParam, changeParamStatus, getParam, addParam, updateParam, delParam } from "@/api/operation/idenTypeParam"

import { Picture as IconPicture } from '@element-plus/icons-vue'


import { Splitpanes, Pane } from "splitpanes"
import "splitpanes/dist/splitpanes.css"

const router = useRouter()
const appStore = useAppStore()
const { proxy } = getCurrentInstance()
const { sys_normal_disable, sys_yes_no, data_type } = proxy.useDict("sys_normal_disable", "sys_yes_no" , "data_type")

const paramList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const idenTypeName = ref("")
const idenTypeOptions = ref(undefined)

const url= import.meta.env.VITE_APP_BASE_API;


//当前选中左侧树(识别类型)id
const currentIdenTypeId = ref(0)
const currentImageUrl = ref("")


const idenTypeTitle = ref("")
const idenTypeOpen = ref(false)




const optionCardShow = ref(false)
const idenTypeCardShow = ref(false)
const optionCardX = ref("")
const optionCardY = ref("")
const optionData = ref("")

// 列显隐信息
const columns = ref([
  { key: 0, label: `参数名称`, visible: true },
  { key: 1, label: `数据类型`, visible: true },
  { key: 2, label: `取值范围`, visible: true },
  { key: 3, label: `状态`, visible: true }
])

const data = reactive({
  form: {},
  idenTypeForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    paramName: undefined,
    idenTypeId: undefined
  },
  rules: {
    paramName: [{ required: true, message: "参数名称不能为空", trigger: "blur" }, { min: 2, max: 200, message: "参数名称长度必须介于 2 和 20 之间", trigger: "blur" }],
    dataType: [{ required: true, message: "数据类型不能为空", trigger: "change" }],
    minValue: [{ required: true, message: "最小值不能为空"}, {pattern: /(^[\-0-9][0-9]*(\.[0-9]+)?)$/, message: "最小值必须输入合法的数字", trigger: "blur" }],
    maxValue: [{ required: true, message: "最大值不能为空"}, {pattern: /(^[\-0-9][0-9]*(\.[0-9]+)?)$/, message: "最大值必须输入合法的数字", trigger: "blur" }],
    enumValue: [{ required: true, message: "取值范围不能为空", trigger: "blur" }]
  }
})

const { queryParams, form, rules, idenTypeForm } = toRefs(data)


function floderOption(e, data, n, t){
  
  optionCardX.value = e.x + 30 // 让右键菜单出现在鼠标右键的位置
  optionCardY.value = e.y + 20
  optionData.value = data
  //this.node = n // 将当前节点保存
  //this.tree = t
  if (data.id == 0) {
    optionCardShow.value = true // 
    idenTypeCardShow.value = false // 
  } else {
    optionCardShow.value = false // 
    idenTypeCardShow.value = true // 
  }

}

function idenTypeCancel(){
  idenTypeOpen.value = false;
}

function closeOptionCard(){
  optionCardShow.value = false // 
  idenTypeCardShow.value = false // 
}

//创建识别类型
function handleCreateIdenType(){
  idenTypeForm.value = {
    id: undefined,
    idenTypeName: undefined,
    samplePhotoUrl: undefined,
    samplePhotoPhysical: undefined,
    haveMinMax: undefined,
    remark: undefined
  }
  idenTypeTitle.value = '新增识别类型';
  idenTypeOpen.value = true;
  closeOptionCard()
}
function handleUpdateIdenType(){

  getIdenType(optionData.value.id).then(response => {
    idenTypeForm.value = response.data
    idenTypeTitle.value = '编辑识别类型';
    idenTypeOpen.value = true;
    closeOptionCard()
  })

}
function handleDeleteIdenType(){
  closeOptionCard()
  proxy.$modal.confirm('是否确认删除识别类型数据项？').then(function () {
    return deleteIdenType(optionData.value.id)
  }).then(() => {
    getIdenTypeTree()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
  
}


/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true
  return data.idenTypeName.indexOf(value) !== -1
}

/** 根据名称筛选部门树 */
watch(idenTypeName, val => {
  proxy.$refs["idenTypeTreeRef"].filter(val)
})

/** 查询参数列表 */
function getList() {
  loading.value = true
  listParam(queryParams.value).then(res => {
    loading.value = false
    paramList.value = res.rows
    total.value = res.total
  })
}

/** 查询识别类型下拉树结构 */
function getIdenTypeTree() {
  idenTypeTreeSelect().then(response => {
    idenTypeOptions.value = response.data
  })
}


/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.idenTypeId = data.id
  currentIdenTypeId.value  = data.id;
  currentImageUrl.value = data.samplePhotoUrl;
  handleQuery()
}



/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  // queryParams.value.idenTypeId = undefined
  // proxy.$refs.idenTypeTreeRef.setCurrentKey(null)
  handleQuery()
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除参数数据项？').then(function () {
    return delParam(ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}



/** 参数状态修改  */
function handleStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用"
  proxy.$modal.confirm('确认要"' + text + '""' + row.paramName + '"参数吗?').then(function () {
    return changeParamStatus(row.id, row.status)
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function () {
    row.status = row.status === "0" ? "1" : "0"
  })
}




/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.userId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}


/** 重置操作表单 */
function reset() {
  form.value = {
    id: undefined,
    idenTypeId: undefined,
    paramName: undefined,
    dataType: undefined,
    minValue: undefined,
    maxValue: undefined,
    enumValue: undefined,
    remark: undefined
  }
  proxy.resetForm("paramRef")
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加参数"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getParam(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改参数"
  })
}

/** 提交按钮 */
function submitForm() {

  if(currentIdenTypeId.value == null || currentIdenTypeId.value == 0 ){
    proxy.$modal.msgError("请选择识别类型");
    return false;
  }
  if(form.value.dataType == 1 ){
    const regex = /(^[\-0-9][0-9]*(\.[0-9]+)?)$/;
    if(!regex.test(form.value.minValue)){
      proxy.$modal.msgError("最小值必须输入合法的数字");
      return false;
    }
    if(!regex.test(form.value.maxValue)){
      proxy.$modal.msgError("最大值必须输入合法的数字");
      return false;
    }

    if(parseFloat(form.value.minValue) > parseFloat(form.value.maxValue)){
      proxy.$modal.msgError("最小值不能大于最大值");
      return false;
    }
  }

  if(form.value.dataType == 2 && form.value.enumValue == ''){
    proxy.$modal.msgError("取值范围不能为空");
    return false;
  }

  proxy.$refs["paramRef"].validate(valid => {
    if (valid) {
      form.value.idenTypeId = currentIdenTypeId;
      if (form.value.id != undefined) {
        updateParam(form.value).then(response => {
          proxy.$modal.msgSuccess("修改识别类型参数成功")
          open.value = false
          getList()
        })
      } else {
        addParam(form.value).then(response => {
          proxy.$modal.msgSuccess("新增识别类型参数成功")
          open.value = false
          getList()
        })
      }
    }
  })
}


function submitIdenTypeForm(){

  if(idenTypeForm.value.idenTypeName == '' || idenTypeForm.value.idenTypeName == undefined){
    proxy.$modal.msgError("识别类型名称不能为空");
    return false;
  }
  if(idenTypeForm.value.haveMinMax == '' || idenTypeForm.value.haveMinMax == undefined){
    proxy.$modal.msgError("是否标记起始点不能为空");
    return false;
  }

  if (idenTypeForm.value.id != undefined) {
    updateIdenType(idenTypeForm.value).then(response => {
      proxy.$modal.msgSuccess("修改识别类型成功")
      idenTypeOpen.value = false
      getIdenTypeTree()
    })
  } else {
    addIdenType(idenTypeForm.value).then(response => {
      proxy.$modal.msgSuccess("新增识别类型成功")
      idenTypeOpen.value = false
      getIdenTypeTree()
    })
  }

}

getIdenTypeTree()
getList()
</script>

<style scoped>
  ::v-deep .row-expand-cover .el-table__expand-icon {
    visibility: hidden;
  }
  .folder-box {
    height: 100%;
  }

  .option-card-button {
    width: 100%;
    margin-left: 0;
    font-size: 10px;
    border-radius: 0;
  }

  .el-button+.el-button {
    margin-left: 0px;
  }

  .el-tree-node:focus>.el-tree-node__content {
    background-color: #66b1ff87 !important;
  }

  .el-tree-node__content:hover {
    background-color: #66b1ff87;
  }

  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: #66b1ff87;
  }

  .expand .el-table__expand-column .cell {
    display: none;
  }
</style>

<style scoped>
.demo-image__error .block {
  /* padding: 30px 0; */
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  display: inline-block;
  width: 99%;
  box-sizing: border-box;
  vertical-align: top;
}
.demo-image__error .demonstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}
.demo-image__error .el-image {
  padding: 0 5px;
  max-width: 300px;
  max-height: 200px;
  width: 100%;
  height: 200px;
}

.demo-image__error .image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}
.demo-image__error .image-slot .el-icon {
  font-size: 30px;
}
</style>