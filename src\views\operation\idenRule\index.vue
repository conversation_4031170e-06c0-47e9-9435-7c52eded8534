<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="规则名称" prop="idenRuleName">
        <el-input
            v-model="queryParams.idenRuleName"
            placeholder="请输入规则名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item>
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="table.tableList" style="width: 100%;">
      <el-table-column label="序号" width="50" type="index" align="center">
        <template #default="scope">
          <span>{{ (table.pageNum - 1) * table.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="规则名称" align="center" prop="idenRuleName" :show-overflow-tooltip="true"/>
      <el-table-column label="规则类别" align="center" prop="idenRuleType" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-for=" item in filteredIdenTypes(scope.row.idenRuleType)">
            {{item.label}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="点位模型" align="center" prop="idenModelId" :show-overflow-tooltip="true">
        <template #default="scope">
          <div v-for=" item in filteredIdenModelId(scope.row.idenModelId)">
            {{item.label}}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="规则数量" align="center" prop="ruleNum" :show-overflow-tooltip="true"/>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)"
                     v-hasPermi="['monitor:online:forceLogout']">编辑
          </el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['iden:rule:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="table.total > 0" :total="table.total" v-model:page="table.pageNum" v-model:limit="table.pageSize"  @pagination="getList"/>

    <!-- 添加规则 -->
    <el-dialog :title="rulesForm.title" v-model="rulesForm.dialogOpen" width="820px" append-to-body>
      <el-form ref="rulesFormRef" :model="rulesForm" :rules="rules" label-width="120px">
        <el-form-item label="规则名称" prop="idenRuleName">
          <el-input v-model="rulesForm.idenRuleName" placeholder="请输入任务名称"/>
        </el-form-item>
        <el-form-item label="规则类别" prop="idenRuleType">
          <el-select v-model="rulesForm.idenRuleType" placeholder="请选择">
            <el-option v-for="dict in iden_rule_type" :key="dict.value" :label="dict.label"
                       :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点位模型" prop="idenModelId">
          <el-select v-model="rulesForm.idenModelId" placeholder="请选择">
            <el-option v-for="dict in idenTypeList" :key="dict.value" :label="dict.label"
                       :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="addSubmitForm()">确 定</el-button>
          <el-button @click="rulesForm.dialogOpen=false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="IdenRule">

import {getIdenTypeList, list as initData, addIdenRule, remove} from "@/api/operation/idenRule"

const {proxy} = getCurrentInstance()
const {iden_rule_type} = proxy.useDict("iden_rule_type")
const table = reactive({tableList: [], total: 0, pageNum: 1, pageSize: 10})
const queryParams = reactive({idenRuleName: ''})
const loading = ref(true)
const data = reactive({
  rulesForm: {dialogOpen: false, title: '', id: '', idenRuleName: '', idenRuleType: '', idenModelId: ''},
  rules: {
    idenRuleName: [{required: true, message: "规则名称不能为空", trigger: "blur"}],
    idenRuleType: [{required: true, message: "规则类别不能为空", trigger: "change"}],
    idenModelId: [{required: true, message: "点位模型不能为空", trigger: "change"}]
  }
})

const {rulesForm, rules} = toRefs(data)

const idenTypeList = [];

/** 查询登录日志列表 */
function initIdenTypeList() {
  loading.value = true
  getIdenTypeList().then(response => {
    idenTypeList.splice(0, idenTypeList.length);
    response.data.forEach(item => idenTypeList.push({value: item.id, label: item.idenTypeName}));
    handleQuery();
  })
}

/** 查询登录日志列表 */
function getList() {
  loading.value = true
  initData({idenRuleName:queryParams.idenRuleName,pageNum:table.pageNum,pageSize:table.pageSize}).then(response => {
    table.tableList = response.rows
    table.total = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  table.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认规则名称为"' + row.idenRuleName + '"的数据项?').then(function () {
    return remove(row.id)
  }).then(() => {
    handleQuery()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {
  })
}

/** 编辑按钮操作 */
function handleEdit(row) {
  console.log(row)
}

/** 添加规则操作 */
function handleAdd() {
  resFrom(true, "添加规则");
}
/** 添加规则操作 */
function addSubmitForm() {
  proxy.$refs["rulesFormRef"].validate(valid => {
    if (valid) {
      addIdenRule(rulesForm.value).then(res => {
        if (res.code == '200') {
          proxy.$modal.msgSuccess("添加成功");
          resFrom(false, '')
          handleQuery();
        } else {
          proxy.$modal.msgError(res.msg);
        }
      })
    }
  })
}


/**重置表单**/
function resFrom(dialogOpen, title) {
  rulesForm.value = {dialogOpen: dialogOpen, title: title, id: '', idenRuleName: '', idenRuleType: '', idenModelId: ''}
  proxy.$refs["rulesFormRef"].resetFields();
}

function filteredIdenModelId(idenModelId) {
  return idenTypeList.filter(item => item.value == idenModelId);
}

function filteredIdenTypes(idenRuleType) {
  return iden_rule_type.value.filter(item => item.value == idenRuleType);
}
/**初始化加载*/
initIdenTypeList()
</script>
