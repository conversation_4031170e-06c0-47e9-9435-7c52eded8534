<template>
	<div class="app-container">

		<el-form :model="queryParams" :inline="true" label-width="90px">
			<el-form-item label="机器人名称">
				<el-input v-model="queryParams.robotName" placeholder="请输入机器人名称" clearable></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handleQuery">搜索</el-button>
				<el-button @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>

		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button type="warning" plain @click="handleImport('批量固件更新', ids)" :disabled="multiple">批量固件更新</el-button>
				<el-button type="primary" plain @click="handleAdd">添加</el-button>
			</el-col>
		</el-row>

		<el-table v-loading="loading" :data="dataList" @selection-change="dataSelection">
			<el-table-column type="selection"></el-table-column>
			<el-table-column label="机器人名称" align="center" prop="robotName" />
			<el-table-column label="型号" align="center" prop="robotModel" />
			<el-table-column label="投运时间" align="center" prop="beginUseTime">
				<template #default="scope">
					{{ parseTime(scope.row.beginUseTime) }}
				</template>
			</el-table-column>
			<el-table-column label="序列号" align="center" prop="sn" />
			<el-table-column label="IP" align="center" prop="ip" />
			<el-table-column label="端口" align="center" prop="port" />
			<el-table-column label="状态" align="center">
				<template #default="scope">
					<dict-tag :options="robot_status" :value="scope.row.status" />
				</template>
			</el-table-column>
			<el-table-column label="优先级" align="center">
				<template #default="scope">
					{{ scope.row.priority }}
				</template>
			</el-table-column>
			<el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
				<template #default="scope">
					<el-button link type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
					<el-button link type="primary" @click="restart(scope.row)">重启</el-button>
					<el-button link type="primary" @click="handleImport('固件更新', scope.row.id)">固件更新</el-button>

					<el-popover placement="right" :width="700" trigger="click">
						<template #reference>
							<el-button link type="primary">硬件配置</el-button>
						</template>
						<el-button type="primary" plain @click="hardwareHandleAdd(scope.row.id)" style="margin-bottom: 10px;">添加</el-button>
						<el-table :data="scope.row.robotHardwareList">
							<el-table-column align="center" prop="hardwareName" label="硬件名称" />
							<el-table-column align="center" prop="sn" label="序列号" />
							<el-table-column align="center" label="类型" >
								<template #default="scope">
									<dict-tag :options="hardware_type" :value="scope.row.hardwareType"></dict-tag>
								</template>
							</el-table-column>
							<el-table-column align="center" prop="position" label="位置" />
							<el-table-column label="IP" align="center" prop="ip" />
							<el-table-column label="端口" align="center" prop="port" />
							<el-table-column label="操作" align="center" width="50" class-name="small-padding fixed-width">
								<template #default="scope">
									<el-button link type="primary" @click="hardwareHandleUpdate(scope.row)">编辑</el-button>
								</template>								
							</el-table-column>
						</el-table>
					</el-popover>

				</template>
			</el-table-column>
		</el-table>
		<pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
			v-model:limit="queryParams.pageSize" @pagination="getList" />

		<el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
			<el-upload ref="uploadRef" :limit="1" accept="" :headers="upload.headers"
				:action="upload.url + '?ids=' + upload.ids" :disabled="upload.isUploading"
				:on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
				<el-icon class="el-icon--upload"><upload-filled /></el-icon>
				<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
				<template #tip>
					<div class="el-upload__tip text-center">
						<!-- <span>仅允许导入xls、xlsx格式文件。</span> -->
					</div>
				</template>
			</el-upload>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitFileForm">确 定</el-button>
					<el-button @click="upload.open = false">取 消</el-button>
				</div>
			</template>
		</el-dialog>

		<el-dialog v-model="dialogVisible" :title="title" :close-on-press-escape="false" :show-close="false"
			:close-on-click-modal="false">
			<el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
				<el-row :gutter="15">
					<el-col :span="12">
						<el-form-item label="机器人名称" prop="robotName">
							<el-input v-model="form.robotName" placeholder="请输入机器人名称"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="型号" prop="robotModel">
							<el-input v-model="form.robotModel" placeholder="请选择型号"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="序列号" prop="sn">
							<el-input v-model="form.sn" type="text" placeholder="请输入SN"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="投运时间" prop="beginUseTime">
							<el-date-picker v-model="form.beginUseTime" type="datetime" placeholder="请选择投运时间" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="IP" prop="ip">
							<el-input v-model="form.ip" type="text" placeholder="请输入IP"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="端口" prop="port">
							<el-input v-model="form.port" type="text" placeholder="请输入端口"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="固件版本" prop="version">
							<el-input v-model="form.version" type="text" placeholder="请输入固件版本"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="App Code" prop="appCode">
							<el-input v-model="form.appCode" type="text" placeholder="请输入App Code"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="优先级" prop="priority">
							<el-input v-model="form.priority" type="number" placeholder="请选择优先级"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="地图" prop="mapId">
							<el-select v-model="form.mapId" type="text" placeholder="请选择地图"></el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="form.remark" type="textarea" placeholder="请输入备注"></el-input>
						</el-form-item>
					</el-col>

				</el-row>
			</el-form>
			<template #footer>
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handelConfirm">确定</el-button>
			</template>
		</el-dialog>

		<el-dialog v-model="hardwareDialogVisible" :title="hardwareTitle" :close-on-press-escape="false"
			:show-close="false" :close-on-click-modal="false">
			<el-form ref="hardwareFormRef" :model="hardwareForm" :rules="hardwareRules" label-width="100px">
				<el-row :gutter="15">
					<el-col :span="12">
						<el-form-item label="硬件名称" prop="hardwareName">
							<el-input v-model="hardwareForm.hardwareName" placeholder="请输入硬件名称"></el-input>
						</el-form-item>
					</el-col>

					<el-col :span="12">
						<el-form-item label="序列号" prop="sn">
							<el-input v-model="hardwareForm.sn" type="text" placeholder="请输入SN"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="类型" prop="hardwareType">
							<el-select v-model="hardwareForm.hardwareType" placeholder="请选择类型">
								<el-option v-for="item in hardware_type" :label="item.label"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="位置" prop="position">
							<el-select v-model="hardwareForm.position" placeholder="请选择位置">
								<el-option v-for="item in hardware_position" :label="item.label"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :span="12">
						<el-form-item label="IP" prop="ip">
							<el-input v-model="hardwareForm.ip" type="text" placeholder="请输入IP"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="端口" prop="port">
							<el-input v-model="hardwareForm.port" type="text" placeholder="请输入端口"></el-input>
						</el-form-item>
					</el-col>

					<el-col :span="12">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="hardwareForm.remark" type="textarea" placeholder="请输入备注"></el-input>
						</el-form-item>
					</el-col>

				</el-row>
			</el-form>
			<template #footer>
				<el-button @click="hardwareDialogVisible = false">取消</el-button>
				<el-button type="primary" @click="hardwareHandelConfirm">确定</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script>
	import {
		getToken
	} from "@/utils/auth";
	import robot from '@/api/robot/robot';
	import robotHardware from '@/api/robot/robotHardware';

	export default {
		components: {

		},
		data() {
			return {
				hardwareDataList: [],
				hardwareTitle: '',
				hardwareRules: {},
				hardwareForm: {},
				hardwareDialogVisible: false,
				hardware_type: getCurrentInstance().proxy.useDict("hardware_type").hardware_type,
				hardware_position: getCurrentInstance().proxy.useDict("hardware_position").hardware_position,
				proxy: getCurrentInstance().proxy,
				robot_status: getCurrentInstance().proxy.useDict("robot_status").robot_status,
				ids: [],
				single: true,
				multiple: true,
				rules: {},
				dialogVisible: false,
				title: '',
				dataList: [],
				loading: false,
				total: 0,
				upload: {
					// 是否显示弹出层
					open: false,
					// 弹出层标题
					title: "",
					// 是否禁用上传
					isUploading: false,
					// 是否更新已经存在的数据
					ids: [],
					// 设置上传的请求头部
					headers: {
						Authorization: "Bearer " + getToken()
					},
					// 上传的地址
					url: import.meta.env.VITE_APP_BASE_API + "/robot/uploadFirmware"
				},
				form: {},
				queryParams: {
					pageNum: 1,
					pageSize: 10
				},
				rules: {}
			}
		},
		mounted() {
			this.getList()
		},
		methods: {
			getList() {
				this.loading = true
				robot.getList(this.queryParams).then(res => {
					this.dataList = res.rows
					this.total = res.total
					this.loading = false
				})

			},
			restart(row) {
				this.$modal.confirm('是否确认重启机器人：' + row.robotName).then(function () {				  
				  return robot.restart(row);
				}).then(() => {
				  this.getList()
				  this.$modal.msgSuccess('重启指令已下发')
				}).catch(() => {})
				
			},
			hardwareHandelConfirm() {
				this.$refs.hardwareFormRef.validate((valid) => {
					if (!valid) return;
					if (!this.hardwareForm.id) {
						robotHardware.add(this.hardwareForm).then(res => {
							this.$modal.msgSuccess('硬件添加成功')
							this.hardwareDialogVisible = false
							this.getList()
						})
					} else {
						robotHardware.edit(this.hardwareForm).then(res => {
							this.$modal.msgSuccess('硬件编辑成功')
							this.hardwareDialogVisible = false
							this.getList()
						})
					}

				})
			},
			handelConfirm() {
				this.$refs.formRef.validate((valid) => {
					if (!valid) return;
					if (!this.form.id) {
						robot.add(this.form).then(res => {
							this.$modal.msgSuccess('添加成功')
							this.dialogVisible = false
							this.getList()
						})
					} else {
						robot.edit(this.form).then(res => {
							this.$modal.msgSuccess('编辑成功')
							this.dialogVisible = false
							this.getList()
						})
					}

				})
			},
			dataSelection(rows) {
				this.ids = rows.map(item => item.id)
				this.single = rows.length != 1
				this.multiple = !rows.length
			},
			handleAdd() {
				this.reset()
				this.title = '机器人添加'
				this.dialogVisible = true
			},
			hardwareHandleAdd(id) {
				this.reset()
				this.hardwareTitle = '硬件添加'
				this.hardwareForm.robotId = id;
				this.hardwareDialogVisible = true
			},
			reset() {
				this.hardwareForm = {}
				this.form = {}
				this.proxy.resetForm('formRef')
				this.proxy.resetForm('hardwareFormRef')
			},
			resetQuery() {
				this.queryParams = {
					pageNum: 1,
					pageSize: 10
				}
			},
			handleQuery() {
				this.getList()
			},
			handleUpdate(row) {
				this.reset()
				this.title = '编辑机器人'
				this.dialogVisible = true
				robot.getOne(row.id).then(res => {
					this.form = res.data
				})
			},
			hardwareHandleUpdate(row) {
				this.reset()
				this.hardwareTitle = '编辑硬件'
				this.hardwareDialogVisible = true
				robotHardware.getOne(row.id).then(res => {
					this.hardwareForm = res.data
				})
			},
			handleImport(title, ids) {
				this.upload.open = true
				this.upload.title = title
				this.upload.ids = ids
			},
			handleFileUploadProgress(event, file, fileList) {
				this.upload.isUploading = true
			},
			handleFileSuccess(response, file, fileList) {
				this.upload.open = false
				this.upload.isUploading = false
				this.$refs["uploadRef"].handleRemove(file)
				console.log(response.msg)
				this.getList()
			},
			submitFileForm() {
				this.$refs["uploadRef"].submit()
			}
		}
	}
</script>

<style>

</style>