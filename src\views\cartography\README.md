# 制图管理模块

## 概述

本模块是从 `old\panorama` 目录迁移并转换为 Vue3 的制图管理页面，完全重构了原有的 Vue2 代码。

## 迁移完成情况

### ✅ 已完成的迁移工作

1. **主要文件结构**
   ```
   src/views/cartography/
   ├── index.vue                    # 主入口文件 (从 old/panorama/index.vue 迁移)
   ├── components/
   │   ├── LeftPanel.vue           # 左侧图层面板
   │   ├── CenterPanel.vue         # CAD图层显示面板
   │   ├── CenterPanelXMap.vue     # XMAP图层调整面板
   │   ├── CenterPanelMain.vue     # 主显示面板 (ECharts渲染)
   │   ├── RightPanel.vue          # 右侧信息面板
   │   └── ImportMapDialog.vue     # 导入图层对话框
   ├── utils/
   │   └── MapEchartsRenderer.js   # ECharts渲染工具类
   └── README.md                   # 本文档
   ```

2. **核心功能迁移**
   - ✅ 地图数据加载和显示
   - ✅ CAD图层上传和预览
   - ✅ XMAP图层上传和偏移调整
   - ✅ 图层切换和管理
   - ✅ 地图信息编辑
   - ✅ ECharts地图渲染
   - ✅ 响应式布局设计

3. **简化的功能需求**
   - ❌ 移除【新建地图】功能 (系统默认唯一地图)
   - ❌ 移除【地图列表选择】功能 (系统默认唯一地图)
   - ✅ 保留核心制图功能

## 技术栈转换

### Vue2 → Vue3 主要变化

1. **Composition API**
   ```javascript
   // Vue2 (Options API)
   export default {
     data() {
       return { currentMap: null }
     },
     methods: {
       fetchMap() { ... }
     }
   }

   // Vue3 (Composition API)
   <script setup>
   import { ref } from 'vue'
   const currentMap = ref(null)
   const fetchMap = () => { ... }
   </script>
   ```

2. **响应式系统**
   - 使用 `ref()` 和 `reactive()` 替代 `data()`
   - 使用 `computed()` 替代 `computed` 选项
   - 使用 `watch()` 替代 `watch` 选项

3. **生命周期钩子**
   - `mounted` → `onMounted()`
   - `beforeUnmount` → `onBeforeUnmount()`

4. **组件通信**
   - 使用 `defineProps()` 和 `defineEmits()`
   - 使用 `defineExpose()` 暴露方法给父组件

### Element UI → Element Plus

1. **组件更新**
   - 图标系统完全重构
   - 组件 API 部分调整
   - 样式变量名称更新

2. **新特性使用**
   - 更好的 TypeScript 支持
   - 更现代的设计风格
   - 更好的无障碍支持

## API 集成

使用 `src/api/dispatch/cartography.js` 中的新 API：

```javascript
// 主要 API 接口
import { 
  getRecentMap,           // 获取最近修改的地图
  updateNameAndScale,     // 更新地图名称和比例尺
  saveOffset             // 保存图层偏移量
} from '@/api/dispatch/cartography'

// 文件上传
// 通过 fetch API 直接调用 /cartography/import-layer 接口
```

## 组件说明

### 1. LeftPanel.vue
- **功能**: 显示地图图层树结构
- **特点**: 支持图层状态显示，点击切换图层
- **迁移**: 从原 panorama 的侧边栏功能简化而来

### 2. CenterPanelMain.vue
- **功能**: 主地图显示区域，使用 ECharts 渲染
- **特点**: 支持点位、路径、障碍物显示
- **迁移**: 保留了原有的 ECharts 渲染逻辑

### 3. CenterPanelXMap.vue
- **功能**: XMAP图层偏移调整
- **特点**: 实时偏移调整，SVG预览
- **迁移**: 核心的偏移调整功能

### 4. CenterPanel.vue
- **功能**: CAD图层单独显示和变换
- **特点**: 支持旋转、缩放、平移
- **迁移**: 简化了原有的变换控制

### 5. RightPanel.vue
- **功能**: 显示地图详细信息
- **特点**: 实时显示图层状态和统计信息
- **迁移**: 整合了原有的信息显示功能

### 6. ImportMapDialog.vue
- **功能**: 图层文件上传对话框
- **特点**: 支持拖拽上传，文件类型验证
- **迁移**: 保留了原有的上传逻辑

## 使用说明

1. **访问页面**: 启动项目后访问 `/cartography/index`
2. **加载地图**: 页面自动加载最新的地图数据
3. **上传图层**: 
   - 点击"导入图层"按钮
   - 先上传 CAD 图层 (.svg)
   - 再上传 XMAP 图层 (.xmap)
4. **调整偏移**: 在 XMAP 图层中调整偏移量并保存
5. **编辑信息**: 点击"编辑地图信息"修改名称和比例尺

## 注意事项

1. **依赖要求**
   - Vue 3.x
   - Element Plus
   - ECharts 5.x
   - SCSS 支持

2. **API 要求**
   - 后端需要提供 cartography 相关接口
   - 文件上传需要正确的认证机制

3. **浏览器兼容性**
   - 现代浏览器 (Chrome 88+, Firefox 85+, Safari 14+)
   - 不支持 IE

## 开发建议

1. **性能优化**
   - 大文件上传进度显示
   - ECharts 渲染优化
   - 图片懒加载

2. **用户体验**
   - 更多的加载状态提示
   - 错误处理优化
   - 快捷键支持

3. **功能扩展**
   - 批量操作支持
   - 历史版本管理
   - 导出功能

## 问题排查

1. **组件加载失败**: 检查组件文件路径和导入语句
2. **API 调用失败**: 检查网络和后端服务状态
3. **样式问题**: 检查 SCSS 编译和 CSS 变量
4. **ECharts 渲染问题**: 检查容器尺寸和数据格式

---

**迁移完成时间**: 2024年12月
**Vue 版本**: 2.x → 3.x
**UI 框架**: Element UI → Element Plus
**状态**: ✅ 迁移完成，功能正常
