<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="任务名称" prop="taskName">
            <el-input
               v-model="queryParams.taskName"
               placeholder="请输入任务名称"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="计划规则" prop="planType">
            <el-select v-model="queryParams.planType" placeholder="请选择计划规则" clearable style="width: 240px">
               <el-option
                  v-for="dict in plan_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['system:config:add']"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Edit"
               :disabled="single"
               @click="handleUpdate"
               v-hasPermi="['system:config:edit']"
            >修改</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['system:config:remove']"
            >删除</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="taskDefList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="任务名称" align="center" prop="taskName" />
         <el-table-column label="优先级" align="center" prop="priorty" :show-overflow-tooltip="true" />
         <el-table-column label="管理部门" align="center" prop="deptName" :show-overflow-tooltip="true" />
         <el-table-column label="机器人数量" align="center" prop="robotNum" :show-overflow-tooltip="true" />
         <el-table-column label="计划规则" align="center" prop="planType" :show-overflow-tooltip="true" >
            <template #default="scope">
                <dict-tag :options="plan_type" :value="scope.row.planType" />
            </template>
         </el-table-column>
         <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
         <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:config:edit']" >修改</el-button>
               <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:config:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
        <el-steps style="max-width: 800px" :active="stepIndex" align-center finish-status="success">
            <el-step title="基础信息" description="" />
            <el-step title="选择点位" description="" />
            <el-step title="选择机器人" description="" />
            <el-step title="规则配置" description="" />
        </el-steps>
         <el-form ref="taskRef" :model="form" :rules="rules" label-width="80px" >
            <div v-if="stepIndex == 1" style="margin-top:30px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="任务名称" prop="taskName">
                        <el-input v-model="form.taskName" placeholder="请输入任务名称" maxlength="200" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="优先级" prop="priorty">
                        <el-input v-model="form.priorty" placeholder="请输入优先级" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="管理部门" prop="deptId">
                            <el-tree-select v-model="form.deptId" :data="enabledDeptOptions" :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id" placeholder="请选择归属部门" check-strictly/>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="24">
                        <el-form-item label="备注">
                        <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>

            <div v-if="stepIndex == 2" class="tree-container"  style="margin-top:30px">
                <el-tree
                    class="tree-border"
                    :data="pointOptions"
                    show-checkbox
                    node-key="id"
                    :check-strictly="false"
                    empty-text="加载中，请稍候"
                    ref="tree"
                    :props="pointDefaultProps"
                    :default-checked-keys="selectedKeys"
                    @check-change="handleCheckChange"
                ></el-tree>
 
            </div>

            <div v-if="stepIndex == 3"  style="margin-top:30px">
                <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="Plus" @click="handleAddRobot">添加机器人</el-button>
                </el-col>
                </el-row>
                <el-table :data="robotDataList">
                    <el-table-column label="机器人名称" align="center" key="robotName" prop="robotName"/>
                    <el-table-column label="主备类型" align="center" key="masterFlag" prop="masterFlag">
                        <template #default="scope">
                            <dict-tag :options="master_flag" :value="scope.row.masterFlag" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                        <el-tooltip content="删除" placement="top">
                            <el-button link type="primary" icon="Delete" @click="handleDeleteRobot(scope.row)"></el-button>
                        </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div v-if="stepIndex == 4"  style="margin-top:30px">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="计划规则" prop="planType">
                            <el-select v-model="form.planType" placeholder="请选择计划规则" clearable>
                                <el-option v-for="dict in plan_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="form.planType == 0">
                        <el-form-item label="开始时间" prop="beginTime">
                            <el-date-picker v-model="form.beginTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="form.planType == 0">
                        <el-form-item label="结束时间" prop="endTime">
                            <el-date-picker v-model="form.endTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="form.planType == 1">
                        <el-form-item label="周期类型" prop="cycleType">
                            <el-select v-model="form.cycleType" placeholder="请选择周期类型" clearable>
                                <el-option v-for="dict in cycle_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="form.planType == 1">
                        <el-form-item label="执行时间" prop="cycleTrigger">
                            <el-time-picker v-model="form.cycleTrigger" type="datetime"  value-format="HH:mm" format="HH:mm">
                            </el-time-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="form.planType == 1">
                        <el-form-item label="周期范围" prop="cycleScope">
                            <el-select v-model="form.cycleScope" placeholder="请选择周期范围" clearable>
                                <el-option v-for="dict in cycle_scope" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
                <el-button v-if="stepIndex != 1" @click="skipTo('pre')">上一步</el-button>&nbsp;
                <el-button v-if="stepIndex != 4" @click="skipTo('next')" >下一步</el-button>&nbsp;
                <el-button v-if="stepIndex == 4" type="primary" @click="submitForm" >保 存</el-button>
            </div>
         </template>
      </el-dialog>

    <el-dialog title="添加机器人" v-model="robotOpen" width="600px" append-to-body>
      <el-form :model="robotForm" :rules="robotRules"  ref="robotRef" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="机器人" prop="robotId">
              <el-select v-model="robotForm.robotId" clearable filterable placeholder="请选择机器人">
                <el-option v-for="item in robotList" :key="item.id" :label="item.robotName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="主备类型" prop="masterFlag">
              <el-select v-model="robotForm.masterFlag" placeholder="请选择主备类型" clearable>
                <el-option v-for="dict in master_flag" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRobotForm">确 定</el-button>
          <el-button @click="robotCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>



   </div>
</template>

<script setup name="Config">
import {  getConfig } from "@/api/system/config"
import { deptTreeSelect } from "@/api/system/user"
import { listTask, getTask, addTask, updateTask, delTask, listAllRobot , pointTreeSelect} from "@/api/operation/taskDef"

const { proxy } = getCurrentInstance()
const { sys_yes_no, plan_type ,master_flag, cycle_type, cycle_scope } = proxy.useDict("sys_yes_no", "plan_type", "master_flag", "cycle_type", "cycle_scope")

const taskDefList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const dateRange = ref([])

const stepIndex = ref(1)
const deptOptions = ref(undefined)
const enabledDeptOptions = ref(undefined)

const tree = ref(null);

//点位树
const pointOptions = ref([])
//选中的节点
const selectedKeys = ref([])
const pointDefaultProps= ref({
    children: "children",
    label: "name"
})



//机器人选择下拉
const robotList = ref([])
//机器人table列表
const robotDataList = ref([])
//添加机器人弹窗
const robotOpen = ref(false)



const data = reactive({
  form: {},
  robotForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    planType: undefined
  },
  rules: {
    taskName: [{ required: true, message: "任务名称不能为空", trigger: "blur" }],
    priorty: [{ required: true, message: "优先级不能为空", trigger: "blur" }, {pattern: /(^[\-0-9][0-9]*(\.[0-9]+)?)$/, message: "优先级必须输入合法的数字", trigger: "blur" }],
    deptId: [{ required: true, message: "管理部门不能为空", trigger: "blur" }],
    configValue: [{ required: true, message: "参数键值不能为空", trigger: "blur" }]
  },
  robotRules: {
    robotId: [{ required: true, message: "机器人不能为空", trigger: "blur" }],
    masterFlag: [{ required: true, message: "主备类型不能为空", trigger: "blur" }]
  }
})

const { queryParams, form, robotForm, rules, robotRules } = toRefs(data)


function  skipTo(type) {
    if (type == "pre") {
        if (stepIndex.value == 1) return;
        stepIndex.value--;
    } else if (type == "next") {

        if(form.value.taskName == null || form.value.taskName == undefined ||  form.value.taskName == ''){
            proxy.$modal.msgError("请输入任务名称")
            return false;
        }
        if(form.value.priorty == null || form.value.priorty == undefined || form.value.taskName == ''){
            proxy.$modal.msgError("请输入优先级")
            return false;
        }

        const regex = /(^[\-0-9][0-9]*(\.[0-9]+)?)$/;
        if(!regex.test(form.value.priorty)){
            proxy.$modal.msgError("优先级须输入合法的数字");
            return false;
        }

        if(form.value.deptId == null || form.value.deptId == undefined){
            proxy.$modal.msgError("请选择管理部门")
            return false;
        }


        if(stepIndex.value == 1){
            getPointTree();
        }
        if(stepIndex.value == 2){
            if(selectedKeys.value.length == 0){
                proxy.$modal.msgError("请选择点位")
                return false;
            }
        }

        if(stepIndex.value == 3){
            if(robotDataList.value.length == 0){
                proxy.$modal.msgError("请选择机器人")
                return false;
            }

            var masterRobot = robotDataList.value.find(r => r.masterFlag == 1 );
            if(masterRobot == null){
                proxy.$modal.msgError("请添加一个主机器人")
                return false;
            }       
        }

        stepIndex.value++;
    }
}

//获取点位树
function getPointTree() {
  pointTreeSelect(form.value.deptId).then(response => {
    pointOptions.value = response.data
  })
}
//树节点选中事件
function  handleCheckChange(data, checked, node) {
    const checkedNodes = tree.value.getCheckedKeys();
    selectedKeys.value = checkedNodes;
}


/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect().then(response => {
    deptOptions.value = response.data
    enabledDeptOptions.value = filterDisabledDept(JSON.parse(JSON.stringify(response.data)))
  })
}

/** 过滤禁用的部门 */
function filterDisabledDept(deptList) {
  return deptList.filter(dept => {
    if (dept.disabled) {
      return false
    }
    if (dept.children && dept.children.length) {
      dept.children = filterDisabledDept(dept.children)
    }
    return true
  })
}


//添加机器人按钮事件
function handleAddRobot(){

    robotOpen.value = true;
    robotForm.value.robotId = undefined;
    robotForm.value.masterFlag = undefined;
}

//机器人删除
function handleDeleteRobot(row){

    robotDataList.value = robotDataList.value.filter(obj => obj.robotId !== row.robotId);

}

//查询所有机器人
function getAllRobot(){
  listAllRobot().then(res => {
    robotList.value = res.data;
  })
}

//添加机器人提交
function submitRobotForm(){

  proxy.$refs["robotRef"].validate(valid => {
    if (valid) {

        var oldRobot = robotDataList.value.find(r => r.robotId == robotForm.value.robotId);
        if(oldRobot != null){
            proxy.$modal.msgError("机器人：【"+oldRobot.robotName+"】已在列表中")
            return false;
        }

        var masterRobot = robotDataList.value.find(r => r.masterFlag == 1 && robotForm.value.masterFlag == 1);
        if(masterRobot != null){
            proxy.$modal.msgError("列表已经有一个主机器人")
            return false;
        }

        var robot = robotList.value.find(r => r.id == robotForm.value.robotId);
        if(robot != null){
            var o = {
                robotId: robotForm.value.robotId,
                robotName: robot.robotName,
                masterFlag: robotForm.value.masterFlag
            }
            robotDataList.value.push(o);
        }   
        robotOpen.value = false
  
    }
  })
}

function robotCancel(){
    robotOpen.value = false;
    robotForm.value = {
        robotId: undefined,
        masterFlag: undefined
    }
    proxy.resetForm("roboteRef")
}






/** 查询参数列表 */
function getList() {
  loading.value = true
  listTask(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    taskDefList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false
  reset()
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    taskName: undefined,
    priorty: undefined,
    deptId: undefined,
    remark: undefined,

    planType: undefined,
    beginTime: undefined,
    endTime: undefined,
    cycleType: undefined,
    cycleTrigger: undefined,
    cycleScope: undefined
  }
  selectedKeys.value = [];
  robotDataList.value = [];
  proxy.resetForm("taskRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.configId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加任务"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getTask(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改任务"

    selectedKeys.value = response.data.bizPointIds;

    robotDataList.value = []
    response.data.taskRobotList.forEach(element => {
        var robot = robotList.value.find(r => r.id == element.robotId);
        if(robot != null){
            var o = {
                robotId: element.robotId,
                robotName: robot.robotName,
                masterFlag: element.masterFlag
            }
            robotDataList.value.push(o);
        } 
    });

    stepIndex.value = 1;
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["taskRef"].validate(valid => {
    if (valid) {

        if(form.value.planType == 0 ){
            if(form.value.beginTime == null || form.value.beginTime == ''){
                proxy.$modal.msgError("请输入开始时间");
                return false;
            }
            if(form.value.endTime == null || form.value.endTime == ''){
                proxy.$modal.msgError("请输入结束时间");
                return false;
            }

        }

        if(form.value.planType == 1 ){
            if(form.value.cycleType == null || form.value.cycleType == ''){
                proxy.$modal.msgError("请选择周期类型");
                return false;
            }
            if(form.value.cycleTrigger == null || form.value.cycleTrigger == ''){
                proxy.$modal.msgError("请输入执行时间");
                return false;
            }
            if(form.value.cycleScope == null || form.value.cycleScope == ''){
                proxy.$modal.msgError("请选择周期范围");
                return false;
            }

        }

        form.value.bizPointIds = selectedKeys.value;
        form.value.taskRobotList = robotDataList.value;
        if (form.value.id != undefined) {
            updateTask(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功")
            open.value = false
            getList()
            })
        } else {
            addTask(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功")
            open.value = false
            getList()
            })
        }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const id = row.id || ids.value
  proxy.$modal.confirm('是否确认删除任务数据项？').then(function () {
    return delTask(id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}


getDeptTree()
getAllRobot()

getList()
</script>

<style>
    /* 或者通过外层容器设置 */
    .tree-container {
        height: 300px;
    }
    .tree-container .el-tree {
        height: 100%;
    }
</style>