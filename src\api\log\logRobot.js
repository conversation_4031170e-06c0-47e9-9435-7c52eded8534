import request from '@/utils/request'

const add = (params) =>{
	return request.post('logRobot/add', params)
}

const remove = (ids) =>{
	return request.get('logRobot/remove/' + ids)
}

const edit = (params) =>{
	return request.post('logRobot/edit', params)
}

const getList = (params) =>{
	return request.get('logRobot/getList', {params: params})
}

const getAll = (params) =>{
	return request.get('logRobot/getAll', {params: params})
}

const getOne = (id) =>{
	return request.get('logRobot/getOne/' + id)
}

export default {
	add, remove, edit, getList, getAll, getOne
}