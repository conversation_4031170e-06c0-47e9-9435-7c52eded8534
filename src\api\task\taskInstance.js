import request from '@/utils/request'

const add = (params) =>{
	return request.post('taskInstance/add', params)
}

const remove = (ids) =>{
	return request.get('taskInstance/remove/' + ids)
}

const edit = (params) =>{
	return request.post('taskInstance/edit', params)
}

const getList = (params) =>{
	return request.get('taskInstance/getList', {params: params})
}

const getAll = (params) =>{
	return request.get('taskInstance/getAll', {params: params})
}

const getOne = (id) =>{
	return request.get('taskInstance/getOne/' + id)
}

const auditData = (ids) =>{
	return request.get('taskInstance/auditData/' + ids)
}

const auditDataEdit = (params) =>{
	return request.post('taskInstance/auditDataEdit', params)
}

const getViewTheReport = (id) =>{
	return request.get('taskInstance/getViewTheReport/' + id)
}

const exportPdf = (params) =>{
	return request({
	  url: 'taskInstance/exportPdf',
	  method: 'post',
	  responseType: 'blob',
	  data: params
	})
}
export default {
	add, remove, edit, getList, getAll, getOne, auditData, auditDataEdit, getViewTheReport, exportPdf
}