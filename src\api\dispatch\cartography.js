import request from '@/utils/request'


// 上传地图CAD或XMAP图层
export function uploadMapLayer(data) {

  // 检查FormData是否包含file字段
  // if (data instanceof FormData) {
  //   console.log('FormData字段:', Array.from(data.keys()));
  // }

  return request({
    url: '/cartography/import-layer',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
      'isToken': true,
      'repeatSubmit': false
    },
    // 添加超时时间，防止上传大文件超时
    timeout: 60000
  }).then(response => {
    console.log('上传成功，响应:', response);
    return response;
  }).catch(error => {
    console.error('上传失败:', error);
    throw error;
  });
}

// 修改xmap图层偏移量
export function saveOffset(data) {
  return request({
    url: '/cartography/save-offset',
    method: 'post',
    data: data
  })
}

// 更新地图信息：只能修改名称和比例尺
export function updateNameAndScale(data) {
  return request({
    url: '/map/main/update',
    method: 'put',
    data: data
  })
}

/**
 * 获取最近修改的地图，用于前端默认展示
 * 返回结构：
{
  "code": 200,
  "msg": "成功",
  "data": {
        "mapId": {
            "type": "Long",
            "description": "地图ID"
        },
        "mapName": {
            "type": "String",
            "description": "地图名称"
        },
        "cadWidth": {
            "type": "Double",
            "description": "CAD图层宽度，取自图层的svg文件定义width（单位是pt）"
        },
        "cadHeight": {
            "type": "Double",
            "description": "CAD图层高度（单位是pt），CAD左上角永远是0,0，右下角是宽高的值"
        },
        "cadUrl": {
            "type": "String",
            "description": "CAD图层svg文件url"
        },
        "scale": {
            "type": "Double",
            "description": "地图比例尺"
        },
        "xmapWidth": {
            "type": "Double",
            "description": "XMAP图层宽度（米）"
        },
        "xmapHeight": {
            "type": "Double",
            "description": "XMAP图层高度（米）"
        },
        "xmapVersion": {
            "type": "Double",
            "description": "XMAP图层版本"
        },
        "minPosX": {
            "type": "Double",
            "description": "最小X轴位置"
        },
        "minPosY": {
            "type": "Double",
            "description": "最小Y轴位置"
        },
        "maxPosX": {
            "type": "Double",
            "description": "最大X轴位置"
        },
        "maxPosY": {
            "type": "Double",
            "description": "最大Y轴位置"
        },
        "offsetX": {
            "type": "Double",
            "description": "XMAP图层二次偏移X轴的单位pt（相对于CAD左上角，向右是正数）"
        },
        "offsetY": {
            "type": "Double",
            "description": "XMAP图层二次偏移Y轴的单位pt（相对于CAD左上角，向下是正数）"
        },
        "xmapSvgWidth": {
            "type": "Double",
            "description": "XMAP图层宽度（pt）"
        },
        "xmapSvgHeight": {
            "type": "Double",
            "description": "XMAP图层高度（pt）"
        },
        "advancedPoints": {
            "type": "List<Position>",
            "description": "全景地图的路径点（偏移后的）"
        },
        "advancedPath": {
            "type": "List<Path>",
            "description": "全景地图的路径（偏移后的）"
        },
        "obstacles": {
            "type": "List<Obstacle>",
            "description": "障碍点信息"
        },
        "mergeSvgUrl": {
            "type": "String",
            "description": "合并后的SVG文件URL（仅用于制图平移预览）"
        }
    }
}

Position结构：
{
    "xmapId": {
        "type": "String",
        "description": "机器人点位ID"
    },
    "instanceName": {
        "type": "String",
        "description": "点位名称"
    },
    "angle": {
        "type": "Double",
        "description": "角度（单位弧度）"
    }
}

Path结构：
{
      "mapId": {
        "type": "long",
        "description": "地图ID"
      },
      "xmapId": {
        "type": "string",
        "description": "XMAP路径ID"
      },
      "instanceName": {
        "type": "string",
        "description": "XMAP路径名称"
      },
      "routeType": {
        "type": "string",
        "description": "路径类型: bezier_curve贝塞尔曲线|straight_line直线|convex凸弧线|concave_arc凹弧线"
      },
      "radian": {
        "type": "double",
        "description": "弧形路径的弧度值(仅弧线类型需要),单位:rad"
      },
      "startPos": {
        "type": "IPosition",
        "description": "XMAP起始点"
      },
      "endPos": {
        "type": "IPosition",
        "description": "XMAP结束点"
      },
      "ctrlPos1": {
        "type": "IPosition",
        "description": "控制点1"
      },
      "ctrlPos2": {
        "type": "IPosition",
        "description": "控制点2"
      },
      "angleCompensation": {
        "type": "double",
        "description": "角度补偿"
      },
      "direction": {
        "type": "string",
        "description": "方向: forward正走 | backward倒走"
      },
      "length": {
        "type": "double",
        "description": "start至end的长度，单位：米"
      },
      "isBackward": {
        "type": "boolean",
        "description": "机器人是否倒着走，非路径的方向"
      }
}

Obstacle结构：
{
      "id": {
        "type": "Long",
        "description": "点位ID"
      },
      "xmapPathId": {
        "type": "String",
        "description": "xmap路径ID"
      },
      "instanceName": {
        "type": "String",
        "description": "障碍点名称"
      },
      "posX": {
        "type": "Double",
        "description": "原始点位坐标X"
      },
      "posY": {
        "type": "Double",
        "description": "原始点位坐标Y"
      },
      "mapId": {
        "type": "Long",
        "description": "地图ID"
      },
      "discoverer": {
        "type": "Long",
        "description": "发现者（机器人）"
      },
      "sceneType": {
        "type": "String",
        "description": "场景类型: escort陪同 | task任务 | task-out任务外"
      },
      "taskId": {
        "type": "Long",
        "description": "发现障碍的任务ID（sceneType=task）"
      }
}
 */
export function getRecentMap() {
  return request({
    url: '/map/main/latest',
    method: 'get'
  })
}


/**
 * 根据ID获取地图信息
 * url[get]: /map/main/{id}
 */
export function getMapById(id) {
    return request({
    url: '/map/main/' + id,
    method: 'get'
    })
}