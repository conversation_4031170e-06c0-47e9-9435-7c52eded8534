import request from '@/utils/request'

const add = (params) =>{
	return request.post('robotHardware/add', params)
}

const remove = (ids) =>{
	return request.get('robotHardware/remove/' + ids)
}

const edit = (params) =>{
	return request.post('robotHardware/edit', params)
}

const getList = (params) =>{
	return request.get('robotHardware/getList', {params: params})
}

const getAll = (params) =>{
	return request.get('robotHardware/getAll', {params: params})
}

const getOne = (id) =>{
	return request.get('robotHardware/getOne/' + id)
}

export default {
	add, remove, edit, getList, getAll, getOne
}