import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/hyc";


// 查询充电桩列表
export function listRobotChargingPile(query) {
    return request({
      url: '/robot/charging/pile/list',
      method: 'get',
      params: query
    })
}

// 查询充电桩详细
export function getRobotChargingPile(id) {
    return request({
      url: '/robot/charging/pile/getById/' + parseStrEmpty(id),
      method: 'get'
    })
  }


// 新增充电桩
export function addRobotChargingPile(data) {
    return request({
      url: '/robot/charging/pile/add',
      method: 'post',
      data: data
    })
  }
  
  // 修改充电桩
  export function updateRobotChargingPile(data) {
    return request({
      url: '/robot/charging/pile/update',
      method: 'put',
      data: data
    })
  }

// 删除充电桩
export function delRobotChargingPile(id) {
    return request({
      url: '/robot/charging/pile/delete/' + id,
      method: 'delete'
    })
  }

  export function listBizPoint() {
    return request({
      url: '/robot/charging/pile/getBizPoint',
      method: 'get'
    })
  }

  