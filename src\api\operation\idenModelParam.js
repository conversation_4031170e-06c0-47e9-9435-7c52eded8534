import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/hyc";


// 查询参数列表
export function listParam(query) {
    return request({
      url: '/iden/model/param/list',
      method: 'get',
      params: query
    })
}

// 查询参数详细
export function getParam(id) {
    return request({
      url: '/iden/model/param/getById/' + parseStrEmpty(id),
      method: 'get'
    })
  }

// 参数状态修改
export function changeParamStatus(id, status) {
    const data = {
        id,
      status
    }
    return request({
      url: '/iden/model/param/updateParamStatus',
      method: 'put',
      data: data
    })
}

// 新增参数
export function addParam(data) {
    return request({
      url: '/iden/model/param/add',
      method: 'post',
      data: data
    })
  }
  
  // 修改参数
  export function updateParam(data) {
    return request({
      url: '/iden/model/param/update',
      method: 'put',
      data: data
    })
  }

// 删除参数
export function delParam(id) {
    return request({
      url: '/iden/model/param/delete/' + id,
      method: 'delete'
    })
  }