import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/hyc";


// 查询识别类型下拉树结构
export function idenModelTreeSelect() {
  return request({
    url: '/iden/model/getIdenModelTree',
    method: 'get'
  })
}

// 查询识别类型详细
export function getIdenModel(id) {
  return request({
    url: '/iden/model/getById/' + parseStrEmpty(id),
    method: 'get'
  })
}

// 新增识别类型
export function addIdenModel(data) {
  return request({
    url: '/iden/model/add',
    method: 'post',
    data: data
  })
}

// 修改识别类型
export function updateIdenModel(data) {
  return request({
    url: '/iden/model/update',
    method: 'put',
    data: data
  })
}

// 删除识别类型
export function deleteIdenModel(id) {
  return request({
    url: '/iden/model/delete/' + id,
    method: 'delete'
  })
}