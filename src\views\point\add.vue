<template>
	<div class="app-container">
		<el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
			<el-row :gutter="15">
				<el-col :span="5">
					<h3>机器人控制</h3>
					<el-select placeholder="请选择机器人" @change="robotSelection"></el-select>
					<span>车体控制</span><br />
					<el-button>前</el-button><br />
					<el-button>后</el-button><br />
					
					<span>伸缩杆</span><br />
					<el-button>上</el-button><br />
					<el-button>下</el-button><br />
					
					<span>可见光摄像头</span><br />
					<el-button>变倍</el-button>
					<el-button>聚焦</el-button>
					<el-button>自动聚焦</el-button>
					<el-button>云台</el-button>
				</el-col>
				<el-col :span="19">
					<el-row :gutter="15">
						<el-col :span="12">
							<div id="divPlugin" style="height: 310px;"></div>
						</el-col>
						<el-col :span="12">
							<div id="divPlugin2" style="height: 310px;"></div>
						</el-col>
						
						<el-col :span="12">
							<el-form-item label="设备树">
								<el-select v-model="form.instanceName" placeholder="请选择设备树"></el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="所属部门"  prop="toolGroup">
								<el-select v-model="form.instanceName" placeholder="请选择所属部门"></el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="Toolgroup">
								<el-input v-model="form.toolGroup" type="text" placeholder="请输入Toolgroup"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="Toolid">
								<el-input v-model="form.toolId" type="text" placeholder="请输入Toolid"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="点位名称" prop="instanceName">
								<el-input v-model="form.instanceName" placeholder="请输入点位名称"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="点位类型" prop="toolGroup">
								<el-select v-model="form.instanceName" placeholder="请选择点位类型"></el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="当前点位ID" prop="instanceName">
								<el-input v-model="form.instanceName" placeholder="请输入当前点位ID"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="云台角度上下" >
								<el-input v-model="form.instanceName" placeholder="停止后自动获取" disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="云台角度左右" >
								<el-input v-model="form.instanceName" placeholder="停止后自动获取" disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="摄像头聚焦" >
								<el-input v-model="form.instanceName" placeholder="停止后自动获取" disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="摄像头变倍" >
								<el-input v-model="form.instanceName" placeholder="停止后自动获取" disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="识别模型" >
								
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="伸缩杆高度" >
								<el-input v-model="form.instanceName" placeholder="停止后自动获取" disabled></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="点位规则">
								<el-select v-model="form.instanceName" placeholder="请选择点位规则"></el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="设备类型">
								<el-select v-model="form.instanceName" placeholder="请选择设备类型"></el-select>
							</el-form-item>
						</el-col>
						
						<el-col>
							<el-form-item>				
								<el-button @click="">重置参数</el-button>
								<el-button @click="recognitionOpen = true">识别配置</el-button>
								<el-button @click="handelConfirm">保存</el-button>
								<el-button @click="">返回</el-button>
							</el-form-item>
						</el-col>
					</el-row>
					
				</el-col>
				
			</el-row>
		</el-form>
		
		<el-dialog v-model="recognitionOpen" width="1000px" append-to-body draggable :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
			<el-row :gutter="15">
				<el-col :span="16">
					<image-editor ref="imageEditor"></image-editor>
				</el-col>
				<el-col :span="8">
					<el-col>						
						<el-button type="text" @click="imageEditorClick">保存</el-button>
						<el-button type="text" @click="recognitionOpen = false">返回</el-button>
					</el-col>
					<el-col>
						<el-button type="text">标记</el-button>
						<el-button type="text">水平线标记/清除</el-button>						
					</el-col>
					<el-col>
						<el-button type="text">识别</el-button>
						<el-input type="textarea"></el-input>
					</el-col>
					
				</el-col>
				<el-col>
					<el-table :data="dataList">
						<el-table-column label="参数名称" align="center" prop="instanceName" />
						<el-table-column label="识别名称" align="center" prop="deptName" />
						<el-table-column label="索引" align="center" prop="toolGroup" />
						<el-table-column label="单位" align="center" prop="toolId" />
						<el-table-column label="偏移量" align="center" prop="className" />
						<el-table-column label="小数点" align="center" prop="className" />
						<el-table-column label="缩放位" align="center" prop="className" />
						<el-table-column label="量程（最小）" align="center" prop="className" />
						<el-table-column label="量程（最大）" align="center" prop="className" />
					</el-table>
				</el-col>
			</el-row>
			
		</el-dialog>
	</div>
</template>

<script>	
	import imageEditor from './imageEditor';
	
	export default {
		components: {
			imageEditor
		},
		data() {
			return {
				dataList: [],
				recognitionOpen: false,
				title: '点位添加',
				formRef: null,
				form: {},
				rules: {
					instanceName: [{
						required: true,
						message: '请输入点位名称',
						trigger: 'blur'
					}],
					deptName: [{
						required: true,
						message: '请输入所属部门',
						trigger: 'blur'
					}],
					toolGroup: [{
						required: true,
						message: '请输入Toolgroup',
						trigger: 'blur'
					}],
					toolId: [{
						required: true,
						message: '请输入Toolid',
						trigger: 'blur'
					}],
					className: [{
						required: true,
						message: '请输入点位类型',
						trigger: 'blur'
					}],
				},
				dialogVisible: false
			}
		},
		mounted() {
			// this.robotInit()
		},
		methods: {
			init() {
				this.dialogVisible = true
			},
			handelConfirm() {
				this.$refs.formRef.validate((valid) => {
					if (!valid) return;


					this.dialogVisible = true
					this.$emit('confirm')
				})
			},
			imageEditorClick() {
				this.$refs.imageEditor.save()
			},
			robotSelection() {
				// 登录
				var szIP = $("#loginip").val(),
					szPort = $("#port").val(),
					szUsername = $("#username").val(),
					szPassword = $("#password").val();
			
				if ("" == szIP || "" == szPort) {
					return;
				}
			
				var szDeviceIdentify = szIP + "_" + szPort;
			
				WebVideoCtrl.I_Login(szIP, szProtoType, szPort, szUsername, szPassword, {
					timeout: 3000,
					success: function (xmlDoc) {
						showOPInfo(szDeviceIdentify + " 登录成功！");
						$("#ip").prepend("<option value='" + szDeviceIdentify + "'>" + szDeviceIdentify + "</option>");
						setTimeout(function () {
							$("#ip").val(szDeviceIdentify);
							setTimeout(function () {
								getChannelInfo();
							}, 1000);
							getDevicePort();
						}, 10);
					},
					error: function (oError) {
						if (ERROR_CODE_LOGIN_REPEATLOGIN === status) {
							showOPInfo(szDeviceIdentify + " 已登录过！");
						} else {
							if (oError.errorCode === 401) {
								showOPInfo(szDeviceIdentify + " 登录失败，已自动切换认证方式！");
							} else {
								showOPInfo(szDeviceIdentify + " 登录失败！", oError.errorCode, oError.errorMsg);
							}
						}
					}
				});
				
				
				
				// 获取通道
				var szDeviceIdentify = $("#ip").val(),
					oSel = $("#channels").empty();
			
				if (null == szDeviceIdentify) {
					return;
				}
			
				// 模拟通道
				WebVideoCtrl.I_GetAnalogChannelInfo(szDeviceIdentify, {
					success: function (xmlDoc) {
						var oChannels = $(xmlDoc).find("VideoInputChannel");
			
						$.each(oChannels, function (i) {
							var id = $(this).find("id").eq(0).text(),
								name = $(this).find("name").eq(0).text();
							if ("" == name) {
								name = "Camera " + (i < 9 ? "0" + (i + 1) : (i + 1));
							}
							oSel.append("<option value='" + id + "' bZero='false'>" + name + "</option>");
						});
						showOPInfo(szDeviceIdentify + " 获取模拟通道成功！");
					},
					error: function (oError) {
						showOPInfo(szDeviceIdentify + " 获取模拟通道失败！", oError.errorCode, oError.errorMsg);
					}
				});
				// 数字通道
				WebVideoCtrl.I_GetDigitalChannelInfo(szDeviceIdentify, {
					success: function (xmlDoc) {
						var oChannels = $(xmlDoc).find("InputProxyChannelStatus");
			
						$.each(oChannels, function (i) {
							var id = $(this).find("id").eq(0).text(),
								name = $(this).find("name").eq(0).text(),
								online = $(this).find("online").eq(0).text();
							if ("false" == online) {// 过滤禁用的数字通道
								return true;
							}
							if ("" == name) {
								name = "IPCamera " + (i < 9 ? "0" + (i + 1) : (i + 1));
							}
							oSel.append("<option value='" + id + "' bZero='false'>" + name + "</option>");
						});
						showOPInfo(szDeviceIdentify + " 获取数字通道成功！");
					},
					error: function (oError) {
						showOPInfo(szDeviceIdentify + " 获取数字通道失败！", oError.errorCode, oError.errorMsg);
					}
				});
				// 零通道
				WebVideoCtrl.I_GetZeroChannelInfo(szDeviceIdentify, {
					success: function (xmlDoc) {
						var oChannels = $(xmlDoc).find("ZeroVideoChannel");
			
						$.each(oChannels, function (i) {
							var id = $(this).find("id").eq(0).text(),
								name = $(this).find("name").eq(0).text();
							if ("" == name) {
								name = "Zero Channel " + (i < 9 ? "0" + (i + 1) : (i + 1));
							}
							if ("true" == $(this).find("enabled").eq(0).text()) {// 过滤禁用的零通道
								oSel.append("<option value='" + id + "' bZero='true'>" + name + "</option>");
							}
						});
						showOPInfo(szDeviceIdentify + " 获取零通道成功！");
					},
					error: function (oError) {
						showOPInfo(szDeviceIdentify + " 获取零通道失败！", oError.errorCode, oError.errorMsg);
					}
				});
				
			
			
				// 开始预览
				var oWndInfo = WebVideoCtrl.I_GetWindowStatus(g_iWndIndex),
					szDeviceIdentify = $("#ip").val(),
					iRtspPort = parseInt($("#rtspport").val(), 10),
					iChannelID = parseInt($("#channels").val(), 10),
					bZeroChannel = $("#channels option").eq($("#channels").get(0).selectedIndex).attr("bZero") == "true" ? true : false,
					szInfo = "";
			
				if ("undefined" === typeof iStreamType) {
					iStreamType = parseInt($("#streamtype").val(), 10);
				}
			
				if (null == szDeviceIdentify) {
					return;
				}
				var startRealPlay = function () {
					WebVideoCtrl.I_StartRealPlay(szDeviceIdentify, {
						iStreamType: iStreamType,
						iChannelID: iChannelID,
						bZeroChannel: bZeroChannel,
						iPort: iRtspPort,
						success: function () {
							console.log(1);
			
							szInfo = "开始预览成功！";
							showOPInfo(szDeviceIdentify + " " + szInfo);
						},
						error: function (oError) {
							console.log(2);
			
							showOPInfo(szDeviceIdentify + " 开始预览失败！", oError.errorCode, oError.errorMsg);
						}
					});
				};
			
				if (oWndInfo != null) {// 已经在播放了，先停止
					WebVideoCtrl.I_Stop({
						success: function () {
							startRealPlay();
						}
					});
				} else {
					startRealPlay();
				}
				
			},
			robotInit() {			
				let that = this
				// 初始化插件参数及插入插件
				WebVideoCtrl.I_InitPlugin({
					bWndFull: true,     //是否支持单窗口双击全屏，默认支持 true:支持 false:不支持
					iWndowType: 1,
					// aIframe: ["test"],
					// cbSelWnd: function (xmlDoc) {
					// 	g_iWndIndex = parseInt($(xmlDoc).find("SelectWnd").eq(0).text(), 10);
					// 	var szInfo = "当前选择的窗口编号：" + g_iWndIndex;
					// 	showCBInfo(szInfo);
					// },
					// cbDoubleClickWnd: function (iWndIndex, bFullScreen) {
					// 	var szInfo = "当前放大的窗口编号：" + iWndIndex;
					// 	if (!bFullScreen) {
					// 		szInfo = "当前还原的窗口编号：" + iWndIndex;
					// 	}
					// 	showCBInfo(szInfo);
					// },
					// cbEvent: function (iEventType, iParam1, iParam2) {
					// 	if (2 == iEventType) {// 回放正常结束
					// 		showCBInfo("窗口" + iParam1 + "回放结束！");
					// 	} else if (-1 == iEventType) {
					// 		showCBInfo("设备" + iParam1 + "网络错误！");
					// 	} else if (3001 == iEventType) {
					// 		clickStopRecord(g_szRecordType, iParam1);
					// 	}
					// },
					cbInitPluginComplete: function () {
						WebVideoCtrl.I_InsertOBJECTPlugin("divPlugin").then(() => {
							// 检查插件是否最新
							WebVideoCtrl.I_CheckPluginVersion().then((bFlag) => {
								if (bFlag) {
									alert("检测到新的插件版本，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
								}
							});
						}, () => {
							alert("插件初始化失败，请确认是否已安装插件；如果未安装，请安装确认后下载的HCWebSDKPluginsUserSetup.exe");
							that.goOnLink()
						});
					}
				});
			
			
			
				// 窗口事件绑定
				$(window).bind({
					resize: function () {
						//WebVideoCtrl.I_Resize($("body").width(), $("body").height());
					}
				});
			
				//初始化日期时间
				// var szCurTime = dateFormat(new Date(), "yyyy-MM-dd");
				// $("#starttime").val(szCurTime + " 00:00:00");
				// $("#endtime").val(szCurTime + " 23:59:59");
				// $("#downloadstarttime").val(szCurTime + " 00:00:00");
				// $("#downloadendtime").val(szCurTime + " 23:59:59");
				
			},			
			goOnLink() {
				const a = document.createElement('a') // 创建一个<a></a>标签
				a.href = '/HCWebSDKPluginsUserSetup.exe' // 给a标签的href属性值加上地址
				a.download = 'HCWebSDKPluginsUserSetup.exe' // 设置下载文件文件名，这里加上.xlsx指定文件类型，pdf文件就指定.fpd即可
				a.style.display = 'none' // 障眼法藏起来a标签
				document.body.appendChild(a) // 将a标签追加到文档对象中
				a.click() // 模拟点击了a标签，会触发a标签的href的读取，浏览器就会自动下载了
				a.remove() // 一次性的，用完就删除a标签
			
			}
		}
	}
</script>

<style scoped>
	
</style>