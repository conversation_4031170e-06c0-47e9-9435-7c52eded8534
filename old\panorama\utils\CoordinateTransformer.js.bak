/**
 * 坐标转换工具类
 * 负责处理pt和px单位之间的转换，以及图层坐标计算
 */

/**
 * 标准pt到px的转换比例
 * 1pt = 1/72英寸，1px = 1/96英寸
 */
export const STANDARD_PT_TO_PX_RATIO = 96 / 72;

/**
 * 坐标转换器类
 */
export default class CoordinateTransformer {
  /**
   * 构造函数
   * @param {Number} pt2pxRatio pt到px的转换比例，默认为标准比例
   */
  constructor(pt2pxRatio = STANDARD_PT_TO_PX_RATIO) {
    this.pt2pxRatio = pt2pxRatio;
    this.baseScale = 1;
    this.baseX = 0;
    this.baseY = 0;
  }

  /**
   * 设置基础缩放和位置
   * @param {Number} scale 基础缩放比例
   * @param {Number} x 基础X坐标
   * @param {Number} y 基础Y坐标
   */
  setBaseTransform(scale, x, y) {
    this.baseScale = scale;
    this.baseX = x;
    this.baseY = y;
  }

  /**
   * 设置pt到px的转换比例
   * @param {Number} ratio 转换比例
   */
  setPt2PxRatio(ratio) {
    this.pt2pxRatio = ratio;
  }

  /**
   * 计算pt单位到px单位的转换比例
   * @param {Number} widthPx 像素宽度
   * @param {Number} heightPx 像素高度
   * @param {Number} widthPt pt单位宽度
   * @param {Number} heightPt pt单位高度
   * @returns {Number} 计算得到的转换比例
   */
  calculatePt2PxRatio(widthPx, heightPx, widthPt, heightPt) {
    if (!widthPt || !heightPt) {
      return STANDARD_PT_TO_PX_RATIO;
    }
    
    // 取宽高比例的最大值，确保图像完全显示
    const ratio = Math.max(
      widthPx / widthPt,
      heightPx / heightPt
    );
    
    this.pt2pxRatio = ratio;
    return ratio;
  }

  /**
   * 将pt单位转换为px单位
   * @param {Number} pt pt单位值
   * @returns {Number} px单位值
   */
  ptToPx(pt) {
    return pt * this.pt2pxRatio;
  }

  /**
   * 将px单位转换为pt单位
   * @param {Number} px px单位值
   * @returns {Number} pt单位值
   */
  pxToPt(px) {
    return px / this.pt2pxRatio;
  }

  /**
   * 计算图层在画布上的位置
   * @param {Number} containerWidth 容器宽度
   * @param {Number} containerHeight 容器高度
   * @param {Number} layerWidthPx 图层像素宽度
   * @param {Number} layerHeightPx 图层像素高度
   * @returns {Object} 包含居中位置和缩放比例的对象
   */
  calculateLayerPosition(containerWidth, containerHeight, layerWidthPx, layerHeightPx) {
    // 计算缩放比例，使图层最大限度填充容器
    const scaleX = containerWidth / layerWidthPx;
    const scaleY = containerHeight / layerHeightPx;
    const scale = Math.min(scaleX, scaleY); // 取较小值，确保完全显示
    
    // 计算居中位置
    const centeredX = (containerWidth - layerWidthPx * scale) / 2;
    const centeredY = (containerHeight - layerHeightPx * scale) / 2;
    
    return {
      scale,
      x: centeredX,
      y: centeredY
    };
  }

  /**
   * 计算点位在ECharts中的坐标
   * @param {Object} point 点位对象，包含newX和newY属性（pt单位）
   * @param {Object} transform 变换对象，包含scale、translateX和translateY属性
   * @param {Number} containerHeight 容器高度
   * @returns {Array} ECharts坐标 [x, y]
   */
  calculatePointPosition(point, transform, containerHeight) {
    // 1. 将pt单位转换为px单位
    const pointXPx = point.newX * this.pt2pxRatio;
    const pointYPx = point.newY * this.pt2pxRatio;
    
    // 2. 计算有效缩放比例
    const activeScale = this.baseScale * transform.scale;
    
    // 3. 计算有效偏移量
    const activeTranslateX = this.baseX + transform.translateX;
    const activeTranslateY = this.baseY + transform.translateY;
    
    // 4. 应用缩放
    const scaledX = pointXPx * activeScale;
    const scaledY = pointYPx * activeScale;
    
    // 5. 应用偏移
    const x = activeTranslateX + scaledX;
    const y = activeTranslateY + scaledY;
    
    // 6. 因为ECharts是左下角原点，需要Y轴翻转
    const finalY = containerHeight - y;
    
    return [x, finalY];
  }
}
