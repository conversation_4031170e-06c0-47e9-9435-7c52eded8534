<template>
  <div class="app-container">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button-group>
        <el-button size="small" @click="handleImportMap">
          <el-icon><Upload /></el-icon>
          导入图层
        </el-button>
        <el-button size="small" @click="handleEditMap" :disabled="!currentMap">
          <el-icon><Edit /></el-icon>
          编辑地图信息
        </el-button>
        <el-button size="small" @click="refreshMapData(currentMap.mapId)" :disabled="!currentMap">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </el-button-group>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧面板 -->
      <left-panel
        ref="leftPanelRef"
        :map-tree-data="mapTreeData"
        :default-props="defaultProps"
        :cad-layer-url="cadLayerUrl"
        :merge-svg-url="mergeSvgUrl"
        :expanded-keys="expandedKeys"
        @node-click="handleNodeClick"
      />

      <!-- 中央面板 - 根据activeLayer显示不同组件 -->
      <center-panel-xmap
        v-if="activeLayer === 'xmap'"
        ref="centerPanelXMapRef"
        :current-map="currentMap"
        :merge-svg-url="mergeSvgUrl"
        :offset="offset"
        :transform-step="transformStep"
        @update-current-map="processMapData"
        @update-offset="updateOffset"
      />

      <center-panel
        v-else-if="activeLayer === 'cad'"
        ref="centerPanelRef"
        :active-layer="activeLayer"
        :current-map="currentMap"
        :cad-layer-url="cadLayerUrl"
        :cad-transform="cadTransform"
      />

      <center-panel-main
        v-else
        ref="centerPanelMainRef"
        :current-map="currentMap"
        :cad-layer-url="cadLayerUrl"
        :cad-transform="cadTransform"
        :show-work-points="showWorkPoints"
        :show-obstacle-points="showObstaclePoints"
      />

      <!-- 右侧面板 -->
      <right-panel
        :current-map="currentMap"
        :cad-width="cadWidth"
        :cad-height="cadHeight"
      />
    </div>

    <!-- 导入地图对话框 -->
    <import-map-dialog
      v-model:visible="importMapDialogVisible"
      :cad-layer-url="cadLayerUrl"
      @file-change="handleImportFileChange"
      @submit="submitImportMap"
      @cancel="importMapDialogVisible = false"
    />

    <!-- 编辑地图信息对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑地图信息"
      width="500px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="地图名称" prop="mapName">
          <el-input v-model="editForm.mapName" placeholder="请输入地图名称" />
        </el-form-item>
        <el-form-item label="比例尺" prop="scale"> <!-- 0.003702881 -->
          <el-input-number
            v-model="editForm.scale"
            placeholder="请输入比例尺"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveMapInfo" :loading="saveLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Cartography">
import { ref, reactive, computed, onMounted, nextTick, getCurrentInstance } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { Upload, Edit, Refresh } from '@element-plus/icons-vue'

// 导入组件
import LeftPanel from './components/LeftPanel.vue'
import CenterPanel from './components/CenterPanel.vue'
import CenterPanelXmap from './components/CenterPanelXMap.vue'
import CenterPanelMain from './components/CenterPanelMain.vue'
import RightPanel from './components/RightPanel.vue'
import ImportMapDialog from './components/ImportMapDialog.vue'

// 导入API
import { uploadMapLayer, updateNameAndScale, getRecentMap, getMapById } from '@/api/dispatch/cartography'

const { proxy } = getCurrentInstance()

// 响应式数据
const currentMap = ref(null)
const cadLayerUrl = ref("")
const mergeSvgUrl = ref("")
const cadWidth = ref(null)
const cadHeight = ref(null)
const activeLayer = ref("center") // 可选值: "center", "cad", "xmap"

// CAD图层变换属性
const cadTransform = reactive({
  rotation: 0,
  scale: 1,
  translateX: 0,
  translateY: 0
})

// XMAP图层基于左上角(0,0)的偏移量(pt)，用于保存
const offset = reactive({
  x: 0,
  y: 0
})

// 变换步长
const transformStep = reactive({
  rotation: 1,    // 旋转步长（度）
  translate: 1    // 平移步长（像素）
})

// 地图树数据
const mapTreeData = ref([])
const defaultProps = {
  children: 'children',
  label: 'label'
}

// 对话框状态
const importMapDialogVisible = ref(false)
const editDialogVisible = ref(false)
const saveLoading = ref(false)

// 表单数据
const editForm = reactive({
  mapName: '',
  scale: null
})

// 表单验证规则
const editRules = {
  mapName: [
    { required: true, message: '请输入地图名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  scale: [
    { required: true, message: '请输入地图缩放比例', trigger: 'blur' },
  ]
}

// 显示标志
const showWorkPoints = ref(false)
const showObstaclePoints = ref(false)

// 导入文件列表
const importFileList = ref([])

// 计算属性
const expandedKeys = computed(() => {
  const keys = []
  mapTreeData.value.forEach(node => {
    if (node.id) {
      keys.push(node.id)
    }
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        if (child.id) {
          keys.push(child.id)
        }
      })
    }
  })
  return keys
})

// 生命周期
onMounted(() => {
  fetchLastModifiedMap()
})

// 方法
// 获取最近一次修改的地图
const fetchLastModifiedMap = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在加载地图数据，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const response = await getRecentMap()
    loading.close()

    if (response.code === 200 && response.data) {
      processMapData(response.data)
      ElMessage.success(`已加载地图: ${response.data.mapName || ''}`)
      refreshComponents()
    } else {
      resetMapData()
    }
  } catch (error) {
    loading.close()
    console.error('获取最近修改的地图失败:', error)
    resetMapData()
    ElMessage.error('获取地图数据失败')
  }
}

// 重置地图数据
const resetMapData = () => {
  mapTreeData.value = []
  currentMap.value = null
  cadLayerUrl.value = ""
  mergeSvgUrl.value = ""
  cadWidth.value = null
  cadHeight.value = null
  offset.x = 0
  offset.y = 0
}

// 处理节点点击
const handleNodeClick = (data) => {
  if (!data.type) {
    // 点击根地图节点时显示Main图层
    activeLayer.value = "center"
    showWorkPoints.value = true
    showObstaclePoints.value = true
    resetCadTransform()

    nextTick(() => {
      if (proxy.$refs.centerPanelMainRef) {
        console.log('通知centerPanelMain更新地图元素')
        proxy.$refs.centerPanelMainRef.updateMapElements()
      }
    })

    const layers = findMapLayers(data)
    const hasValidLayer = layers.some(layer => {
      if (layer.type === 'cad' && cadLayerUrl.value) return true
      if (layer.type === 'xmap' && mergeSvgUrl.value) return true
      return false
    })

    if (hasValidLayer) {
      nextTick(() => {
        if (proxy.$refs.centerPanelMainRef) {
          proxy.$refs.centerPanelMainRef.updateMapElements()
        }
      })
      ElMessage.success(`已选择: ${data.label}`)
    }
  } else {
    // 根据节点类型处理
    switch (data.type) {
      case 'cad':
        activeLayer.value = "cad"
        if (cadTransform.scale !== 1) {
          cadTransform.scale = 1
        }
        if (cadLayerUrl.value) {
          showWorkPoints.value = false
          showObstaclePoints.value = false
          ElMessage.success(`已选择: ${data.label}`)
        } else {
          ElMessage.info('请先上传CAD图层')
        }
        break
      case 'xmap':
        if (mergeSvgUrl.value) {
          activeLayer.value = "xmap"
          showWorkPoints.value = false
          showObstaclePoints.value = false
          ElMessage.success(`已选择: ${data.label}`)
        } else {
          ElMessage.info('请先上传XMAP图层')
        }
        break
      default:
        ElMessage.info(`未知图层类型: ${data.type}`)
        break
    }
  }
}

// 查找地图下的所有图层
const findMapLayers = (mapNode) => {
  if (!mapNode.children) {
    return []
  }
  return mapNode.children.filter(node => node.type === 'cad' || node.type === 'xmap')
}

// 导入地图
const handleImportMap = () => {
  importMapDialogVisible.value = true
  importFileList.value = []
}

// 导入文件变更处理
const handleImportFileChange = (file) => {
  importFileList.value = [file]
}

// 提交导入地图
const submitImportMap = async (fileData) => {
  const file = fileData.file || fileData
  const isXMAPFile = fileData.isXMAPFile || false
  const isCADFile = fileData.isCADFile || false

  if (!file || !file.raw) {
    ElMessage.error('文件对象不可用')
    return
  }

  if (!currentMap.value) {
    ElMessage.error('请先创建或打开地图')
    return
  }

  if (isXMAPFile && !cadLayerUrl.value) {
    ElMessage.error('CAD图层URL为空，请先导入CAD图层')
    return
  }

  if (isCADFile && cadLayerUrl.value) {
    ElMessage.warning('CAD图层已存在，不允许重复导入')
    return
  }

  const formData = new FormData()
  formData.append('file', file.raw)
  formData.append('mapId', currentMap.value.mapId)

  const loading = ElLoading.service({
    lock: true,
    text: '正在导入地图，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    // 使用 cartography.js 中的 API 上传文件
    const result = await uploadMapLayer(formData)
    loading.close()

    if (result.code === 200) {
      // 上传成功后统一刷新地图数据
      ElMessage.success(`${isXMAPFile ? 'XMAP' : 'CAD'}图层上传成功，正在刷新地图数据...`)
      importMapDialogVisible.value = false

      // 调用 refreshMapData 方法刷新地图数据
      await refreshMapData(currentMap.value.mapId)
    } else {
      ElMessage.error(result.msg || '导入地图失败')
    }
  } catch (error) {
    loading.close()
    console.error('导入地图失败:', error)
    ElMessage.error('导入地图失败')
  }
}

// 刷新地图数据
const refreshMapData = async (mapId) => {
  if (!mapId) {
    ElMessage.error('地图ID无效，无法刷新')
    return
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在刷新地图数据，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    // 使用 getMapById API 根据地图ID获取最新数据
    console.log('刷新地图数据，使用地图ID:', mapId)
    const response = await getMapById(mapId)
    loading.close()

    if (response.code === 200 && response.data) {
      const mapData = response.data
      processMapData(mapData)
      ElMessage.success(`地图数据刷新成功: ${mapData.mapName || ''}`)
      activeLayer.value = 'center'
      refreshComponents()
    } else {
      ElMessage.error(response.msg || '刷新地图数据失败')
    }
  } catch (error) {
    loading.close()
    console.error('刷新地图数据失败:', error)
    ElMessage.error('刷新地图数据失败')
  }
}

// 处理地图数据
const processMapData = (mapData) => {
  console.log('获取地图信息', mapData)

  // 创建地图节点
  const mapNode = {
    id: mapData.mapId,
    label: mapData.mapName || '',
    // scale: mapData.scale || 0.05,
    // remark: mapData.remark || '',
    // xmapWidth: mapData.xmapWidth,
    // xmapHeight: mapData.xmapHeight,
    // xmapVersion: mapData.xmapVersion,
    // minPosX: mapData.minPosX,
    // minPosY: mapData.minPosY,
    // maxPosX: mapData.maxPosX,
    // maxPosY: mapData.maxPosY,
    // xmapSvgWidth: mapData.xmapSvgWidth,
    // xmapSvgHeight: mapData.xmapSvgHeight,
    // mergeSvgUrl: mapData.mergeSvgUrl,
    // offset: { x: mapData.offsetX || 0, y: mapData.offsetY || 0 },
    children: [{
      id: `${mapData.mapId}-1`,
      label: 'CAD图层',
      visible: true,
      type: 'cad'
    }, {
      id: `${mapData.mapId}-2`,
      label: 'XMAP图层',
      visible: true,
      type: 'xmap'
    }]
  }

  mapTreeData.value = [mapNode]

  // 设置当前地图
  currentMap.value = {
    ...mapData
    // id: mapData.mapId,
    // name: mapData.mapName || '',
    // scale: mapData.scale || 0.05,
    // remark: mapData.remark || '',
    // xmapWidth: mapData.xmapWidth,
    // xmapHeight: mapData.xmapHeight,
    // xmapVersion: mapData.xmapVersion,
    // minPosX: mapData.minPosX,
    // minPosY: mapData.minPosY,
    // maxPosX: mapData.maxPosX,
    // maxPosY: mapData.maxPosY,
    // xmapSvgWidth: mapData.xmapSvgWidth,
    // xmapSvgHeight: mapData.xmapSvgHeight,
    // mergeSvgUrl: mapData.mergeSvgUrl,
    // offset: { x: mapData.offsetX || 0, y: mapData.offsetY || 0 },
    // advancedPoints: mapData.advancedPoints || [],
    // advancedRoutes: mapData.advancedPath || [],
    // obstacles: mapData.obstacles || [],
  }

  // 设置图层URL和尺寸
  cadLayerUrl.value = mapData.cadUrl || ""
  cadWidth.value = mapData.cadWidth || null
  cadHeight.value = mapData.cadHeight || null
  mergeSvgUrl.value = mapData.mergeSvgUrl || ""

  // 设置偏移量（pt单位）
  offset.x = mapData.offsetX || 0
  offset.y = mapData.offsetY || 0

  // 记录点位和路径信息
  if (mapData.advancedPoints && mapData.advancedPoints.length > 0) {
    console.log('地图包含点位信息:', mapData.advancedPoints.length, '个点位')
  }

  if (mapData.advancedPath && mapData.advancedPath.length > 0) {
    console.log('地图包含路径信息:', mapData.advancedPath.length, '条路径')
  }

  if (mapData.obstacles && mapData.obstacles.length > 0) {
    console.log('地图包含障碍点信息:', mapData.obstacles.length, '个障碍点')
  }
}

// 刷新组件
const refreshComponents = () => {
  nextTick(() => {
    if (proxy.$refs.leftPanelRef) {
      proxy.$refs.leftPanelRef.refresh()
    }

    if (activeLayer.value === 'xmap') {
      console.log('等待CenterPanelXMap组件自动初始化')
      setTimeout(() => {
        if (proxy.$refs.centerPanelXMapRef) {
          proxy.$refs.centerPanelXMapRef.updateMapElements()
        }
      }, 100)
    } else if (activeLayer.value === 'cad') {
      setTimeout(() => {
        if (proxy.$refs.centerPanelRef) {
          proxy.$refs.centerPanelRef.updateMapElements()
        }
      }, 100)
    } else if (proxy.$refs.centerPanelMainRef) {
      setTimeout(() => {
        if (proxy.$refs.centerPanelMainRef) {
          proxy.$refs.centerPanelMainRef.updateMapElements()
        }
      }, 100)
    }
  })
}

// 重置CAD图层变换
const resetCadTransform = () => {
  cadTransform.rotation = 0
  cadTransform.scale = 1
  cadTransform.translateX = 0
  cadTransform.translateY = 0
}

// 编辑地图信息
const handleEditMap = () => {
  if (!currentMap.value) {
    ElMessage.warning('请先加载地图数据')
    return
  }

  editForm.mapName = currentMap.value.mapName || ''
  editForm.scale = currentMap.value.scale || null
  editDialogVisible.value = true
}

// 保存地图信息
const saveMapInfo = async () => {
  try {
    await proxy.$refs.editFormRef.validate()

    saveLoading.value = true
    const updateData = {
      mapId: currentMap.value.mapId,
      mapName: editForm.mapName,
      scale: editForm.scale
    }

    console.log(updateData)
    await updateNameAndScale(updateData)

    // 更新本地数据
    currentMap.value.mapName = editForm.mapName
    currentMap.value.scale = editForm.scale

    ElMessage.success('地图信息更新成功')
    editDialogVisible.value = false
  } catch (error) {
    console.error('保存地图信息失败:', error)
    ElMessage.error('保存地图信息失败')
  } finally {
    saveLoading.value = false
  }
}

// 更新偏移量
const updateOffset = (newOffset) => {
  offset.x = newOffset.x
  offset.y = newOffset.y
}
</script>

<style lang="scss" scoped>
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color);
}

.toolbar {
  padding: 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .el-button-group {
    .el-button {
      border-radius: 4px;

      &:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      &:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }

      &:not(:first-child):not(:last-child) {
        border-radius: 0;
      }
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;

  // 左侧面板样式
  :deep(.left-panel) {
    width: 280px;
    min-width: 280px;
    background: var(--el-bg-color);
    border-right: 1px solid var(--el-border-color);
    overflow-y: auto;
  }

  // 中央面板样式
  :deep(.center-panel),
  :deep(.center-panel-main),
  :deep(.center-panel-xmap) {
    flex: 1;
    background: var(--el-bg-color);
    position: relative;
    overflow: hidden;
  }

  // 右侧面板样式
  :deep(.right-panel) {
    width: 320px;
    min-width: 320px;
    background: var(--el-bg-color);
    border-left: 1px solid var(--el-border-color);
    overflow-y: auto;
  }
}

// 对话框样式调整
:deep(.el-dialog) {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid var(--el-border-color-light);
  }
}

// 表单样式
:deep(.el-form) {
  .el-form-item {
    margin-bottom: 18px;

    .el-form-item__label {
      font-weight: 500;
      color: var(--el-text-color-regular);
    }

    .el-input-number {
      width: 100%;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    :deep(.right-panel) {
      width: 280px;
      min-width: 280px;
    }
  }
}

@media (max-width: 768px) {
  .toolbar {
    padding: 12px;

    .el-button-group {
      .el-button {
        padding: 8px 12px;
        font-size: 12px;
      }
    }
  }

  .main-content {
    flex-direction: column;

    :deep(.left-panel) {
      width: 100%;
      height: 200px;
      border-right: none;
      border-bottom: 1px solid var(--el-border-color);
    }

    :deep(.right-panel) {
      width: 100%;
      height: 200px;
      border-left: none;
      border-top: 1px solid var(--el-border-color);
    }
  }
}
</style>
