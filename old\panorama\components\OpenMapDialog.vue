<template>
  <el-dialog
    title="打开地图"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @closed="handleClosed"
  >
    <div class="open-map-dialog">
      <!-- 搜索框 -->
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="请输入地图名称搜索"
          clearable
          prefix-icon="el-icon-search"
          @clear="fetchMapList"
          @keyup.enter.native="fetchMapList"
        >
          <el-button slot="append" icon="el-icon-search" @click="fetchMapList"></el-button>
        </el-input>
      </div>

      <!-- 地图列表 -->
      <div class="map-list">
        <el-table
          v-loading="loading"
          :data="mapList"
          border
          style="width: 100%"
          height="350px"
          @row-click="handleRowClick"
          highlight-current-row
        >
          <el-table-column prop="mapName" label="地图名称" min-width="120"></el-table-column>
          <el-table-column prop="scale" label="比例尺" width="100">
            <template slot-scope="scope">
              {{ scope.row.scale }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template slot-scope="scope">
              {{ scope.row.createTime }}
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="120"></el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :disabled="!selectedMap">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getPanoramaPage } from '@/api/robot/panorama'

export default {
  name: "OpenMapDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      searchQuery: '',
      mapList: [],
      selectedMap: null,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mapName: undefined
      }
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.fetchMapList();
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false);
      }
    }
  },
  methods: {
    // 获取地图列表
    fetchMapList() {
      this.loading = true;

      // 设置查询参数
      if (this.searchQuery) {
        this.queryParams.mapName = this.searchQuery;
      } else {
        this.queryParams.mapName = undefined;
      }

      getPanoramaPage(this.queryParams).then(response => {
        this.loading = false;
        if (response.code === 200) {
          this.mapList = response.rows || [];
          this.total = response.total || 0;
        } else {
          this.mapList = [];
          this.total = 0;
          this.$message.error(response.msg || '获取地图列表失败');
        }
      }).catch(error => {
        this.loading = false;
        console.error('获取地图列表失败:', error);
        this.$message.error('获取地图列表失败');
      });
    },
    // 处理行点击
    handleRowClick(row) {
      this.selectedMap = row;
    },
    // 处理页码变化
    handleCurrentChange(currentPage) {
      this.queryParams.pageNum = currentPage;
      this.fetchMapList();
    },
    // 处理每页条数变化
    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.fetchMapList();
    },
    // 处理提交
    handleSubmit() {
      if (this.selectedMap) {
        this.$emit('submit', this.selectedMap);
        this.dialogVisible = false;
      } else {
        this.$message.warning('请先选择一个地图');
      }
    },
    // 处理对话框关闭
    handleClosed() {
      this.searchQuery = '';
      this.selectedMap = null;
      this.mapList = [];
      this.queryParams.pageNum = 1;
      this.queryParams.mapName = undefined;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";
</style>
