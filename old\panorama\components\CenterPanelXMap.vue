<template>
  <div class="center-panel">
    <div class="map-container">
      <!-- 没有选择地图或没有上传XMAP图层时显示空状态 -->
      <div class="placeholder-map" v-if="!currentMap || !mergeSvgUrl">
        <el-empty description="请先上传XMAP图层"></el-empty>
      </div>
      <!-- 有地图且已上传XMAP图层时显示内容 -->
      <div class="map-content" v-else>
        <!-- 使用ECharts显示XMAP图层 -->
        <div ref="echartsContainer" class="echarts-container"></div>
      </div>

      <!-- 基本控制按钮 -->
      <div class="zoom-controls">
        <!-- 移动控制按钮 -->
        <div class="zoom-btn move-up" @click="moveUp" title="上移XMAP图层">
          <i class="el-icon-arrow-up"></i>
        </div>
        <div class="zoom-btn move-down" @click="moveDown" title="下移XMAP图层">
          <i class="el-icon-arrow-down"></i>
        </div>
        <div class="zoom-btn move-left" @click="moveLeft" title="左移XMAP图层">
          <i class="el-icon-arrow-left"></i>
        </div>
        <div class="zoom-btn move-right" @click="moveRight" title="右移XMAP图层">
          <i class="el-icon-arrow-right"></i>
        </div>
        <!-- 设置按钮 -->
        <div class="zoom-btn settings" @click="toggleAdvancedControls" title="XMAP图层高级设置">
          <i class="el-icon-setting"></i>
        </div>
      </div>

      <!-- 高级控制面板 -->
      <advanced-controls
        v-if="showAdvancedControls"
        :xmap-transform="xmapTransform"
        :transform-step="transformStep"
        @update:xmap-transform="updateXmapTransform"
        @save-transform="handleSaveMap"
        @reset-transform="resetTransform"
        @close="showAdvancedControls = false"
      />
    </div>
  </div>
</template>

<script>
import { saveOffsetPanorama } from '@/api/robot/panorama';
import AdvancedControls from './AdvancedControls.vue';
import * as echarts from 'echarts';

export default {
  name: "center-panel-xmap",
  components: {
    AdvancedControls
  },
  props: {
    currentMap: {
      type: Object,
      default: null
    },
    mergeSvgUrl: {
      type: String,
      default: ""
    },
    offset: {
      type: Object,
      required: true
    },
    transformStep: {
      type: Object,
      required: true
    }
  },
  watch: {
    // 监听mergeSvgUrl变化，更新图表
    mergeSvgUrl(newValue) {
      console.log('CenterPanelXMap: mergeSvgUrl变化为', newValue);
      if (newValue) {
        this.$nextTick(() => {
          this.initEcharts();
        });
      }
    },
    // 监听变换属性变化，更新图表
    xmapTransform: {
      handler() {
        this.updateEchartsLayers();
      },
      deep: true
    },
  },
  data() {
    return {
      showAdvancedControls: false,
      echartsInstance: null,
      // mergeSvgUrl的像素尺寸
      mergeSvgWidthPx: 0,
      mergeSvgHeightPx: 0,
      // pt到px的转换比例
      pt2px: 96 / 72,
      // 用户交互状态
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0,
      // 视图状态 - 用于记忆缩放和平移
      viewState: {
        scale: 1,
        translateX: 0,
        translateY: 0
      },
      // 缩放配置
      zoomConfig: {
        min: 0.2,  // 最小缩放比例
        max: 20,    // 最大缩放比例
        step: 0.1  // 每次缩放步长
      }
    };
  },
  mounted() {
    console.log('CenterPanelXMap组件已挂载');

    // 确保在DOM更新后初始化
    this.$nextTick(() => {
      console.log('CenterPanelXMap组件DOM已更新，初始化ECharts');

      // 检查是否有mergeSvgUrl
      if (this.mergeSvgUrl) {
        console.log('CenterPanelXMap: 发现mergeSvgUrl，初始化ECharts');
        this.initEcharts();
      } else {
        console.log('CenterPanelXMap: 没有mergeSvgUrl，跳过初始化');
      }
    });

    // 初始化xmapTransform
    this.xmapTransform = {
      translateX: this.offset.x,
      translateY: this.offset.y
    };

    // 初始化视图状态
    this.viewState = {
      scale: 1,
      translateX: 0, // 将pt单位转换为px单位
      translateY: 0
    };

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', this.resizeEcharts);
  },
  beforeDestroy() {
    // 移除事件监听和销毁图表实例
    window.removeEventListener('resize', this.resizeEcharts);

    // 清理鼠标事件监听
    this.cleanupMouseEvents();

    // 销毁ECharts实例
    if (this.echartsInstance) {
      this.echartsInstance.dispose();
    }
  },
  methods: {
    // 初始化ECharts实例
    initEcharts() {
      console.log('CenterPanelXMap.initEcharts 被调用');

      // 确保DOM已经渲染
      this.$nextTick(() => {
        try {
          // 如果已经有实例，先销毁
          if (this.echartsInstance) {
            console.log('销毁现有的ECharts实例');
            this.echartsInstance.dispose();
            this.echartsInstance = null;
          }

          // 如果没有mergeSvgUrl，不初始化
          if (!this.mergeSvgUrl) {
            console.log('没有mergeSvgUrl，不初始化ECharts');
            return;
          }

          // 检查容器是否存在
          if (!this.$refs.echartsContainer) {
            console.error('ECharts容器不存在，无法初始化');
            return;
          }

          // 初始化ECharts实例
          this.echartsInstance = echarts.init(this.$refs.echartsContainer);

          // 添加鼠标事件监听
          this.setupMouseEvents();

          // 预加载图片以获取尺寸
          this.preloadImages().then(() => {
            // 设置图表选项
            this.updateEchartsLayers();
          }).catch(error => {
            console.error('预加载图片失败:', error);
          });
        } catch (error) {
          console.error('初始化ECharts实例时出错:', error);
        }
      });
    },

    // 预加载图片以获取尺寸
    preloadImages() {
      return new Promise((resolve, reject) => {
        if (!this.mergeSvgUrl) {
          console.warn('没有整合的XMAP图片，无法预加载图片');
          // 设置默认尺寸
          this.mergeSvgWidthPx = 800;
          this.mergeSvgHeightPx = 600;
          resolve();
          return;
        }

        // 从mergeSvgUrl中获取尺寸
        const img = new Image();
        img.onload = () => {
          // 保存图片像素尺寸(px)
          this.mergeSvgWidthPx = img.width;
          this.mergeSvgHeightPx = img.height;
          console.log('mergeSvgUrl像素尺寸px:', this.mergeSvgWidthPx, 'x', this.mergeSvgHeightPx);
          resolve();
        };
        img.onerror = () => {
          console.error('mergeSvgUrl加载失败');
          // 设置默认尺寸
          this.mergeSvgWidthPx = 800;
          this.mergeSvgHeightPx = 600;
          reject(new Error('图片加载失败'));
        };
        img.src = this.mergeSvgUrl;
      });
    },

    // 更新ECharts图层
    updateEchartsLayers() {
      if (!this.echartsInstance) return;

      // 获取容器尺寸
      const containerWidth = this.$refs.echartsContainer.clientWidth;
      const containerHeight = this.$refs.echartsContainer.clientHeight;

      // 基础配置
      const option = {
        backgroundColor: '#f5f7fa',
        animation: false,
        hoverLayerThreshold: Infinity,
        grid: {
          show: false,
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          containLabel: false
        },
        xAxis: {
          show: false,
          type: 'value',
          min: 0,
          max: containerWidth
        },
        yAxis: {
          show: false,
          type: 'value',
          min: 0,
          max: containerHeight
        },
        series: [],
        graphic: []
      };

      // 添加XMAP图层
      if (this.mergeSvgUrl) {
        // 确保有有效的尺寸
        if (!this.mergeSvgWidthPx || !this.mergeSvgHeightPx) {
          console.warn('没有有效的mergeSvgUrl尺寸，使用默认尺寸');
          this.mergeSvgWidthPx = 800;
          this.mergeSvgHeightPx = 600;
        }

        console.log('添加XMAP图层，使用尺寸:', this.mergeSvgWidthPx, 'x', this.mergeSvgHeightPx);

        // 应用视图状态（缩放和平移）
        const { scale, translateX, translateY } = this.viewState;

        // 添加mergeSvgUrl图层 - 应用缩放和平移
        option.graphic.push({
          type: 'image',
          id: 'mergeSvgLayer',
          style: {
            image: this.mergeSvgUrl,
            opacity: 1,
            width: this.mergeSvgWidthPx * scale,
            height: this.mergeSvgHeightPx * scale
          },
          position: [translateX, translateY], // 应用平移
          origin: [0, 0],
          z: 2,
          draggable: false
        });

        // 添加辅助网格（可选，帮助用户了解位置）
        if (scale > 1.5) {
          // 只在放大到一定程度时显示网格
          const gridSize = 50 * scale; // 网格大小随缩放变化
          const gridColor = 'rgba(200, 200, 200, 0.2)'; // 浅灰色半透明

          // 添加水平网格线
          for (let y = 0; y < this.mergeSvgHeightPx * scale; y += gridSize) {
            option.graphic.push({
              type: 'line',
              shape: {
                x1: translateX,
                y1: translateY + y,
                x2: translateX + this.mergeSvgWidthPx * scale,
                y2: translateY + y
              },
              style: {
                stroke: gridColor,
                lineWidth: 1
              },
              z: 1
            });
          }

          // 添加垂直网格线
          for (let x = 0; x < this.mergeSvgWidthPx * scale; x += gridSize) {
            option.graphic.push({
              type: 'line',
              shape: {
                x1: translateX + x,
                y1: translateY,
                x2: translateX + x,
                y2: translateY + this.mergeSvgHeightPx * scale
              },
              style: {
                stroke: gridColor,
                lineWidth: 1
              },
              z: 1
            });
          }
        }
      }

      // 设置图表选项
      this.echartsInstance.setOption(option, true);

      // 设置鼠标样式
      if (this.$refs.echartsContainer) {
        this.$refs.echartsContainer.style.cursor = this.isDragging ? 'grabbing' : 'grab';
      }
    },

    // 调整图表大小
    resizeEcharts() {
      if (this.echartsInstance) {
        this.echartsInstance.resize();
        this.updateEchartsLayers();
      }
    },

    // 控制面板相关方法
    toggleAdvancedControls() {
      this.showAdvancedControls = !this.showAdvancedControls;
    },

    updateXmapTransform(newTransform) {
      // 更新本地offset属性
      this.offset.x = newTransform.translateX;
      this.offset.y = newTransform.translateY;
      this.xmapTransform = {
        translateX: this.offset.x,
        translateY: this.offset.y
      };
      // // 更新图层显示
      // this.updateEchartsLayers();
    },

    // 移动方法
    moveUp() {
      // 更新XMAP图层变换属性
      this.offset.y -= this.transformStep.translate;
      this.xmapTransform = {
        translateX: this.offset.x,
        translateY: this.offset.y
      };
      // 保存变更
      this.handleSaveMap();
      // // 更新图层显示
      // this.updateEchartsLayers();
    },

    moveDown() {
      // 更新XMAP图层变换属性
      this.offset.y += this.transformStep.translate;
      this.xmapTransform = {
        translateX: this.offset.x,
        translateY: this.offset.y
      };
      // 保存变更
      this.handleSaveMap();
      // // 更新图层显示
      // this.updateEchartsLayers();
    },

    moveLeft() {
      // 更新XMAP图层变换属性
      this.offset.x -= this.transformStep.translate;
      this.xmapTransform = {
        translateX: this.offset.x,
        translateY: this.offset.y
      };
      // 保存变更
      this.handleSaveMap();
      // // 更新图层显示
      // this.updateEchartsLayers();
    },

    moveRight() {
      // 更新XMAP图层变换属性
      this.offset.x += this.transformStep.translate;
      this.xmapTransform = {
        translateX: this.offset.x,
        translateY: this.offset.y
      };
      // 保存变更
      this.handleSaveMap();
      // // 更新图层显示
      // this.updateEchartsLayers();
    },

    resetTransform() {
      // 重置XMAP图层变换属性，这是给后端的，与前端无关！
      this.offset = { x: 0, y: 0 };
      this.xmapTransform = {
        translateX: 0,
        translateY: 0
      };
      // 保存变更
      this.handleSaveMap();
      // // 更新图层显示
      // this.updateEchartsLayers();
    },

    // 保存地图
    handleSaveMap() {
      if (!this.currentMap) {
        this.$message.warning('请先选择地图');
        return;
      }

      // 确保使用的是地图ID而不是图层ID
      let mapId = this.currentMap.id;

      // 确保mapId是字符串
      mapId = String(mapId);

      if (mapId && mapId.includes('-')) {
        // 如果是图层ID，提取地图ID部分
        mapId = mapId.split('-')[0];
        console.log('检测到图层ID，已提取地图ID部分:', mapId);
      }

      // 创建请求数据，包含pt单位的offsetX和offsetY
      const requestData = {
        mapId: mapId,
        offsetX: this.offset.x,
        offsetY: this.offset.y
      };

      console.log('保存地图数据:', requestData);

      // 显示加载中提示
      const loading = this.$loading({
        lock: true,
        text: '正在保存地图，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 调用API保存偏移量
      saveOffsetPanorama(requestData).then(response => {
        loading.close();

        if (response.code === 200) {
          this.$message.success('地图保存成功，pt单位偏移量：(' + this.offset.x.toFixed(2) + ', ' + this.offset.y.toFixed(2) + ')');

          // 通知父组件更新offset
          this.$emit('update:offset', { x: this.offset.x, y: this.offset.y });

          // 如果响应中包含新的mergeSvgUrl，则更新并刷新图层
          if (response.data && response.data.mergeSvgUrl) {
            this.refreshXMapLayer(response.data.mergeSvgUrl);
            // 这里更新父组件的currentMap
            this.$emit('update-current-map', response.data);
          } else {
            // 否则只刷新图层显示
            this.updateEchartsLayers();
          }
        } else {
          this.$message.error(response.msg || '保存失败');
        }
      }).catch(error => {
        loading.close();
        console.error('保存地图失败:', error);
        this.$message.error('保存地图失败');
      });
    },

    // 设置鼠标事件监听
    setupMouseEvents() {
      if (!this.echartsInstance || !this.$refs.echartsContainer) return;

      const container = this.$refs.echartsContainer;

      // 鼠标滚轮事件 - 缩放
      container.addEventListener('wheel', this.handleMouseWheel);

      // 鼠标按下事件 - 开始拖动
      container.addEventListener('mousedown', this.handleMouseDown);

      // 鼠标移动事件 - 拖动中
      container.addEventListener('mousemove', this.handleMouseMove);

      // 鼠标松开事件 - 结束拖动
      container.addEventListener('mouseup', this.handleMouseUp);
      container.addEventListener('mouseleave', this.handleMouseUp);

      console.log('已设置鼠标事件监听');
    },

    // 清理鼠标事件监听
    cleanupMouseEvents() {
      if (!this.$refs.echartsContainer) return;

      const container = this.$refs.echartsContainer;

      container.removeEventListener('wheel', this.handleMouseWheel);
      container.removeEventListener('mousedown', this.handleMouseDown);
      container.removeEventListener('mousemove', this.handleMouseMove);
      container.removeEventListener('mouseup', this.handleMouseUp);
      container.removeEventListener('mouseleave', this.handleMouseUp);

      console.log('已清理鼠标事件监听');
    },

    // 处理鼠标滚轮事件 - 缩放
    handleMouseWheel(event) {
      event.preventDefault();

      // 获取鼠标位置相对于容器的坐标
      const rect = this.$refs.echartsContainer.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      // 计算缩放方向和大小
      const delta = event.deltaY > 0 ? -this.zoomConfig.step : this.zoomConfig.step;
      const newScale = Math.max(
        this.zoomConfig.min,
        Math.min(this.zoomConfig.max, this.viewState.scale + delta)
      );

      // 如果缩放没有变化，不进行操作
      if (newScale === this.viewState.scale) return;

      // 计算缩放中心点相对于图像的位置
      const imageX = mouseX - this.viewState.translateX;
      const imageY = mouseY - this.viewState.translateY;

      // 计算新的平移量，保持鼠标位置不变
      const scaleFactor = newScale / this.viewState.scale;
      const newTranslateX = mouseX - imageX * scaleFactor;
      const newTranslateY = mouseY - imageY * scaleFactor;

      // 更新视图状态
      this.viewState = {
        scale: newScale,
        translateX: newTranslateX,
        translateY: newTranslateY
      };

      // 更新图层显示
      this.updateEchartsLayers();

      // 注意：缩放操作不触发保存，因为后端API不支持保存缩放比例
      // 我们只在视图中保持缩放状态，不发送到服务器
    },

    // 处理鼠标按下事件 - 开始拖动
    handleMouseDown(event) {
      // 只响应左键
      if (event.button !== 0) return;

      this.isDragging = true;
      this.lastMouseX = event.clientX;
      this.lastMouseY = event.clientY;

      // 改变鼠标样式
      this.$refs.echartsContainer.style.cursor = 'grabbing';
    },

    // 处理鼠标移动事件 - 拖动中
    handleMouseMove(event) {
      if (!this.isDragging) return;

      // 计算鼠标移动距离
      const deltaX = event.clientX - this.lastMouseX;
      const deltaY = event.clientY - this.lastMouseY;

      // 更新鼠标位置
      this.lastMouseX = event.clientX;
      this.lastMouseY = event.clientY;

      // 更新视图状态
      this.viewState.translateX += deltaX;
      this.viewState.translateY += deltaY;

      // 更新图层显示
      this.updateEchartsLayers();

      // 注意：拖动过程中不触发保存，只在拖动结束后才考虑保存
    },

    // 处理鼠标松开事件 - 结束拖动
    handleMouseUp() {
      if (!this.isDragging) return;

      this.isDragging = false;

      // 恢复鼠标样式
      this.$refs.echartsContainer.style.cursor = 'grab';

      // 如果需要保存偏移量到服务器，可以在这里设置标志
      // 但我们不在这里触发保存，因为用户可能只是临时查看
      // 只有当用户点击"保存"按钮时才保存
    },

    // 注意：我们不在缩放和拖动时自动保存
    // 用户需要点击"保存"按钮才会触发保存操作

    // 刷新XMAP图层
    refreshXMapLayer(newMergeSvgUrl) {
      console.log('刷新XMAP图层，新的mergeSvgUrl:', newMergeSvgUrl);

      // 保存当前的视图状态，以便在刷新后恢复
      const currentViewState = { ...this.viewState };

      // 如果提供了新的mergeSvgUrl，则更新本地变量
      if (newMergeSvgUrl) {
        // 通知父组件更新mergeSvgUrl
        this.$emit('update:merge-svg-url', newMergeSvgUrl);

        // 预加载新图片
        const img = new Image();
        img.onload = () => {
          // 更新图片尺寸
          this.mergeSvgWidthPx = img.width;
          this.mergeSvgHeightPx = img.height;
          console.log('新的mergeSvgUrl像素尺寸px:', this.mergeSvgWidthPx, 'x', this.mergeSvgHeightPx);

          // 恢复之前的视图状态
          this.viewState = currentViewState;

          // 更新ECharts图层
          this.updateEchartsLayers();
        };
        img.onerror = () => {
          console.error('新的mergeSvgUrl加载失败');

          // 恢复之前的视图状态
          this.viewState = currentViewState;

          // 仍然尝试更新图层
          this.updateEchartsLayers();
        };
        img.src = newMergeSvgUrl;
      } else {
        // 如果没有提供新的mergeSvgUrl，则只更新图层
        // 恢复之前的视图状态
        this.viewState = currentViewState;

        this.updateEchartsLayers();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";
</style>
