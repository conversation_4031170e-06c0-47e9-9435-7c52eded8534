import request from '@/utils/request'

// 查询点位模型
export function getIdenTypeList(query) {
    return request({
        url: '/iden/rule/idenTypeList',
        method: 'get',
        params: query
    })
}
// 查询在线用户列表
export function list(query) {
    return request({
        url: '/iden/rule/list',
        method: 'get',
        params: query
    })
}

// 删除菜单
export function addIdenRule(data) {
    return request({
        url: '/iden/rule/addIdenRule',
        method: 'post',
        data: data
    })
}
// 删除菜单
export function remove(id) {
    return request({
        url: '/iden/rule/remove/' + id,
        method: 'delete'
    })
}

