<template>
  <div class="center-panel-main">
    <div class="map-container">
      <!-- 没有选择地图或没有上传任何图层时显示空状态 -->
      <div class="placeholder-map" v-if="!currentMap || !cadLayerUrl">
        <el-empty description="请选择或新建地图">
          <template #image>
            <el-icon size="64" color="var(--el-color-info)">
              <MapLocation />
            </el-icon>
          </template>
        </el-empty>
      </div>
      <!-- 有地图且上传了CAD图层时显示内容 -->
      <div class="map-content" v-else>
        <!-- 使用ECharts显示地图图层 -->
        <div ref="echartsContainer" class="echarts-container"></div>
      </div>
    </div>
  </div>
</template>

<script setup name="CenterPanelMain">
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { MapLocation } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { calculateBezierPoints, calculateArcPoints } from '../utils/MapEchartsRenderer'

// Props
const props = defineProps({
  currentMap: {
    type: Object,
    default: null
  },
  cadLayerUrl: {
    type: String,
    default: ""
  },
  cadTransform: {
    type: Object,
    required: true
  },
  showWorkPoints: {
    type: Boolean,
    default: false
  },
  showObstaclePoints: {
    type: Boolean,
    default: false
  }
})

// Refs
const echartsContainer = ref(null)

// Data
const echartsInstance = ref(null)
const cadWidthPx = ref(0)
const cadHeightPx = ref(0)
const cadWidth = ref(0)
const cadHeight = ref(0)
const pt2px = ref(96 / 72)
const baseScale = ref(1)
const baseX = ref(0)
const baseY = ref(0)

// 用户交互状态
const isDragging = ref(false)
const lastMouseX = ref(0)
const lastMouseY = ref(0)

// 视图状态 - 用于记忆缩放和平移
const viewState = ref({
  scale: 1,
  translateX: 0,
  translateY: 0
})

// 缩放配置
const zoomConfig = {
  min: 0.2,  // 最小缩放比例
  max: 20,   // 最大缩放比例
  step: 0.1  // 每次缩放步长
}

// Watchers
watch(() => props.cadLayerUrl, () => {
  initEcharts()
})

watch(() => props.cadTransform, () => {
  updateEchartsLayers()
}, { deep: true })

watch(() => props.currentMap, () => {
  updateMapElements()
}, { deep: true })

watch(() => props.showWorkPoints, () => {
  updateEchartsLayers()
})

watch(() => props.showObstaclePoints, () => {
  updateEchartsLayers()
})

// Lifecycle
onMounted(() => {
  setTimeout(() => {
    initEcharts()
  }, 100)
  
  window.addEventListener('resize', resizeEcharts)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeEcharts)
  cleanupMouseEvents()
  
  if (echartsInstance.value) {
    echartsInstance.value.dispose()
  }
})

// Methods
const initEcharts = () => {
  console.log('CenterPanelMain.initEcharts 被调用')

  nextTick(() => {
    try {
      if (echartsInstance.value) {
        console.log('销毁现有的ECharts实例')
        echartsInstance.value.dispose()
        echartsInstance.value = null
      }

      if (!props.cadLayerUrl) {
        console.log('没有CAD图层URL，不初始化ECharts')
        return
      }

      if (!echartsContainer.value) {
        console.error('ECharts容器不存在，无法初始化')
        return
      }

      const containerWidth = echartsContainer.value.clientWidth
      const containerHeight = echartsContainer.value.clientHeight

      console.log('ECharts容器尺寸:', containerWidth, 'x', containerHeight)

      if (containerWidth <= 0 || containerHeight <= 0) {
        console.error('ECharts容器尺寸为0，延迟初始化')
        setTimeout(() => {
          initEcharts()
        }, 200)
        return
      }

      echartsInstance.value = echarts.init(echartsContainer.value)
      setupMouseEvents()

      preloadImages().then(() => {
        updateEchartsLayers()
        echartsInstance.value.on('click', handleChartClick)
      }).catch(error => {
        console.error('预加载图片失败:', error)
      })
    } catch (error) {
      console.error('初始化ECharts实例时出错:', error)
    }
  })
}

const preloadImages = () => {
  const promises = []

  if (props.cadLayerUrl) {
    const cadPromise = new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        cadWidthPx.value = img.width
        cadHeightPx.value = img.height

        const cadWidthPt = props.currentMap && props.currentMap.cadWidth
        const cadHeightPt = props.currentMap && props.currentMap.cadHeight

        if (cadWidthPt && cadHeightPt) {
          pt2px.value = Math.max(
            cadWidthPx.value / cadWidthPt,
            cadHeightPx.value / cadHeightPt
          )
          cadWidth.value = cadWidthPt
          cadHeight.value = cadHeightPt
        } else {
          pt2px.value = 96 / 72
          cadWidth.value = cadWidthPx.value / pt2px.value
          cadHeight.value = cadHeightPx.value / pt2px.value
        }

        resolve()
      }
      img.onerror = () => {
        console.error('CAD图层加载失败')
        resolve()
      }
      img.src = props.cadLayerUrl
    })
    promises.push(cadPromise)
  }

  return Promise.all(promises)
}

const updateEchartsLayers = () => {
  if (!echartsInstance.value) return

  const containerWidth = echartsContainer.value.clientWidth
  const containerHeight = echartsContainer.value.clientHeight

  let cadScale = 1
  if (cadWidthPx.value && cadHeightPx.value) {
    const scaleX = containerWidth / cadWidthPx.value
    const scaleY = containerHeight / cadHeightPx.value
    cadScale = Math.min(scaleX, scaleY)
  }

  const option = {
    backgroundColor: '#f5f7fa',
    animation: false,
    hoverLayerThreshold: Infinity,
    grid: {
      show: false,
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      containLabel: false
    },
    xAxis: {
      show: false,
      type: 'value',
      min: 0,
      max: containerWidth
    },
    yAxis: {
      show: false,
      type: 'value',
      min: 0,
      max: containerHeight
    },
    tooltip: {
      trigger: 'item',
      confine: true,
      enterable: true,
      appendToBody: true,
      backgroundColor: 'rgba(50,50,50,0.9)',
      borderColor: '#333',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#fff'
      }
    },
    series: [],
    graphic: []
  }

  // 添加CAD图层
  if (props.cadLayerUrl) {
    const centeredX = (containerWidth - cadWidthPx.value * cadScale) / 2
    const centeredY = (containerHeight - cadHeightPx.value * cadScale) / 2

    const finalScale = cadScale * props.cadTransform.scale * viewState.value.scale
    const finalX = centeredX + props.cadTransform.translateX + viewState.value.translateX
    const finalY = centeredY + props.cadTransform.translateY + viewState.value.translateY

    option.graphic.push({
      type: 'image',
      id: 'cadLayer',
      style: {
        image: props.cadLayerUrl,
        opacity: 1,
        width: cadWidthPx.value,
        height: cadHeightPx.value
      },
      position: [finalX, finalY],
      rotation: props.cadTransform.rotation * Math.PI / 180,
      scale: [finalScale, finalScale],
      origin: [0, 0],
      z: 1,
      draggable: false
    })

    baseScale.value = cadScale
    baseX.value = centeredX
    baseY.value = centeredY
  }

  // 添加工作点位和路径
  addWorkPointsAndRoutes(option, containerWidth, containerHeight)

  echartsInstance.value.setOption(option, true)
}

const addWorkPointsAndRoutes = (option, containerWidth, containerHeight) => {
  // 这里可以添加点位和路径的渲染逻辑
  // 由于代码较长，暂时简化处理
  console.log('添加工作点位和路径', props.showWorkPoints, props.showObstaclePoints)
}

const resizeEcharts = () => {
  if (echartsInstance.value) {
    echartsInstance.value.resize()
    updateEchartsLayers()
  }
}

const setupMouseEvents = () => {
  // 设置鼠标事件
  console.log('设置鼠标事件')
}

const cleanupMouseEvents = () => {
  // 清理鼠标事件
  console.log('清理鼠标事件')
}

const handleChartClick = (params) => {
  console.log('图表点击事件:', params)
}

const updateMapElements = () => {
  console.log('更新地图元素')
  if (echartsInstance.value) {
    updateEchartsLayers()
  }
}

// 暴露方法给父组件
defineExpose({
  updateMapElements
})
</script>

<style lang="scss" scoped>
.center-panel-main {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.map-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.placeholder-map {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-content {
  height: 100%;
  position: relative;
}

.echarts-container {
  width: 100%;
  height: 100%;
  background: #f5f7fa;
}
</style>
