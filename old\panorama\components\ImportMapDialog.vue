<template>
  <el-dialog title="导入地图" :visible.sync="visible" width="500px" @close="handleClose">
    <el-upload
      ref="upload"
      class="upload-demo"
      drag
      action="#"
      :http-request="handleImportUpload"
      :limit="1"
      :file-list="fileList"
      :auto-upload="false"
      :on-change="handleFileChange"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div class="el-upload__tip" slot="tip">
        <p class="important-tip">注意：</p>
        <p class="important-tip">1. 必须优先导入CAD图层</p>
        <p class="important-tip">2. CAD图层不允许重复导入</p>
        <p class="important-tip">3. CAD图层只能上传svg矢量图文件</p>
        <p class="important-tip">4. XMAP图层只能上传xmap格式文件</p>
      </div>
    </el-upload>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "ImportMapDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    cadLayerUrl: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      fileList: []
    };
  },
  methods: {
    handleFileChange(file) {
      this.fileList = [file];
      this.$emit('file-change', file);
    },
    handleImportUpload() {
      // 这个方法不再直接处理上传，而是由submitImportMap方法处理
      console.log('handleImportUpload被调用，但不再直接处理上传');
      return true; // 返回true，阻止组件默认的上传行为
    },
    handleSubmit() {
      if (this.fileList.length === 0) {
        this.$message.warning('请选择要导入的文件');
        return;
      }

      // 获取文件扩展名
      const fileName = this.fileList[0].name;
      const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();

      // 判断是否为CAD文件（svg格式）
      const isCADFile = fileExt === 'svg';

      // 判断是否为XMAP文件（xmap格式）
      const isXMAPFile = fileExt === 'xmap';

      console.log('ImportMapDialog - 文件类型:', { isCADFile, isXMAPFile });
      console.log('ImportMapDialog - 当前CAD图层URL:', this.cadLayerUrl);

      // 判断1：当CAD图层为空时，要求必须先导入CAD图层
      if (isXMAPFile && !this.cadLayerUrl) {
        this.$message.warning('必须先导入CAD图层，再导入XMAP图层');
        return;
      }

      // 判断2：当已经存在CAD图层时，不允许重复导入
      if (isCADFile && this.cadLayerUrl) {
        this.$message.warning('CAD图层已存在，不允许重复导入');
        return;
      }

      // 将文件类型信息一并传递给父组件
      this.$emit('submit', {
        file: this.fileList[0],
        isCADFile,
        isXMAPFile
      });
    },
    handleCancel() {
      this.fileList = [];
      this.$emit('cancel');
    },
    handleClose() {
      this.fileList = [];
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";

/* 组件特定样式 */
.important-tip {
  color: #E6A23C;
  font-weight: bold;
  margin: 2px 0;
}

.el-upload__tip p {
  margin: 3px 0;
  line-height: 1.4;
}
</style>
