<template>
  <div class="right-panel">
    <div class="info-panel">
      <!-- 基本信息部分 -->
      <div class="info-section">
        <div class="info-section-header">
          <span class="info-section-title">基本信息</span>
        </div>
        <div class="info-section-content">
          <div class="info-item">
            <span class="info-label">地图：</span>
            <span class="info-value">{{ currentMap ? currentMap.name : '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">比例尺：</span>
            <span class="info-value">{{ currentMap && currentMap.scale }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">备注：</span>
            <span class="info-value">{{ currentMap && currentMap.remark ? currentMap.remark : '-' }}</span>
          </div>
        </div>
      </div>

      <!-- CAD信息部分 -->
      <div class="info-section">
        <div class="info-section-header">
          <span class="info-section-title">CAD信息</span>
        </div>
        <div class="info-section-content">
          <div class="info-item">
            <span class="info-label">CAD宽度：</span>
            <span class="info-value">{{ formatNumber(cadWidth, '(pt)') }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">CAD高度：</span>
            <span class="info-value">{{ formatNumber(cadHeight, '(pt)') }}</span>
          </div>
        </div>
      </div>

      <!-- XMAP相关信息 -->
      <div v-if="currentMap && (currentMap.xmapWidth || currentMap.xmapHeight || currentMap.xmapVersion)" class="info-section">
        <div class="info-section-header">
          <span class="info-section-title">XMAP信息</span>
        </div>
        <div class="info-section-content">
          <div class="info-item">
            <span class="info-label">XMAP宽度：</span>
            <span class="info-value">{{ formatNumber(currentMap.xmapWidth, '(m)') }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">XMAP高度：</span>
            <span class="info-value">{{ formatNumber(currentMap.xmapHeight, '(m)') }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">XMAP面积：</span>
            <span class="info-value">{{ calculateXmapArea() }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">XMAP版本：</span>
            <span class="info-value">{{ currentMap.xmapVersion || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 坐标信息 -->
      <div v-if="currentMap && (currentMap.minPosX !== undefined || currentMap.minPosY !== undefined || currentMap.maxPosX !== undefined || currentMap.maxPosY !== undefined)" class="info-section">
        <div class="info-section-header">
          <span class="info-section-title">坐标范围</span>
        </div>
        <div class="info-section-content">
          <div class="info-item">
            <span class="info-label">最小X坐标：</span>
            <span class="info-value">{{ currentMap.minPosX !== undefined ? currentMap.minPosX : '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">最小Y坐标：</span>
            <span class="info-value">{{ currentMap.minPosY !== undefined ? currentMap.minPosY : '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">最大X坐标：</span>
            <span class="info-value">{{ currentMap.maxPosX !== undefined ? currentMap.maxPosX : '-' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">最大Y坐标：</span>
            <span class="info-value">{{ currentMap.maxPosY !== undefined ? currentMap.maxPosY : '-' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "RightPanel",
  props: {
    currentMap: {
      type: Object,
      default: null
    },
    cadWidth: {
      type: [Number, String],
      default: null
    },
    cadHeight: {
      type: [Number, String],
      default: null
    }
  },
  methods: {
    // 格式化数字，保留2位小数
    formatNumber(value, unit = '') {
      if (value === null || value === undefined || value === '') {
        return '-';
      }
      // 将值转换为数字并保留2位小数
      const num = parseFloat(value);
      if (isNaN(num)) {
        return '-';
      }
      return num.toFixed(2) + unit;
    },

    // 计算XMAP面积
    calculateXmapArea() {
      if (this.currentMap && this.currentMap.xmapWidth && this.currentMap.xmapHeight) {
        // 计算面积（宽度 × 高度）
        const area = this.currentMap.xmapWidth * this.currentMap.xmapHeight;
        // 格式化为整数
        return Math.round(area) + ' (m²)';
      }
      return '-';
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";

/* 组件特定样式 */
.right-panel {
  width: 260px;
  border-left: 1px solid #e5e7eb;
  padding: 16px;
}

.info-panel {
  background-color: #f9fafb;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.info-section {
  margin-bottom: 16px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section-header {
  background-color: #e1effe;
  padding: 8px 12px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  border-bottom: 1px solid #bfdbfe;
}

.info-section-title {
  color: #2563eb;
  font-weight: 500;
  font-size: 14px;
}

.info-section-content {
  padding: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #4b5563;
  width: 50%;
}

.info-value {
  color: #1f2937;
  font-weight: 500;
  width: 50%;
  text-align: right;
  word-break: break-word;
}
</style>


