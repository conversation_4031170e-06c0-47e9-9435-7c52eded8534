<template>
  <el-dialog
    title="检修配置"
    :visible.sync="visible"
    width="90%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="maintenance-config-dialog"
    @closed="handleClosed"
  >
    <div class="maintenance-config-container">
      <!-- 左侧主图控件 -->
      <div class="map-panel">
        <maintenance-map-panel
          ref="mapPanel"
          :current-map="currentMap"
          :cad-layer-url="cadLayerUrl"
          :cad-transform="cadTransform"
          :show-work-points="true"
          @point-click="handlePointClick"
        />
      </div>

      <!-- 右侧检修点列表控件 -->
      <div class="point-list-panel">
        <maintenance-point-list
          ref="pointList"
          :points="maintenancePoints"
          :selected-point="selectedPoint"
          @select-point="handleSelectPoint"
          @update-point="handleUpdatePoint"
          @delete-point="handleDeletePoint"
          @add-point="handleAddPoint"
        />
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSave">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import MaintenanceMapPanel from './MaintenanceMapPanel.vue';
import MaintenancePointList from './MaintenancePointList.vue';

export default {
  name: 'MaintenanceConfig',
  components: {
    MaintenanceMapPanel,
    MaintenancePointList
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentMap: {
      type: Object,
      default: null
    },
    cadLayerUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 检修点列表
      maintenancePoints: [],
      // 当前选中的检修点
      selectedPoint: null,
      // CAD图层变换属性
      cadTransform: {
        rotation: 0,
        scale: 1,
        translateX: 0,
        translateY: 0
      }
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.initData();
      }
    }
  },
  methods: {
    // 初始化数据
    initData() {
      if (this.currentMap && this.currentMap.advancedPoints) {
        // 过滤出检修点（可以根据实际需求调整过滤条件）
        this.maintenancePoints = this.currentMap.advancedPoints.filter(
          point => point.className === 'MaintenancePoint' || point.className === 'JobPoint'
        );
      } else {
        this.maintenancePoints = [];
      }
      this.selectedPoint = null;
    },

    // 处理点击地图上的点位
    handlePointClick(point) {
      this.selectedPoint = point;
    },

    // 处理选择列表中的点位
    handleSelectPoint(point) {
      this.selectedPoint = point;
      // 通知地图面板高亮显示该点位
      if (this.$refs.mapPanel) {
        this.$refs.mapPanel.highlightPoint(point);
      }
    },

    // 处理更新点位
    handleUpdatePoint(point) {
      const index = this.maintenancePoints.findIndex(p => p.id === point.id);
      if (index !== -1) {
        this.maintenancePoints.splice(index, 1, point);
      }
    },

    // 处理删除点位
    handleDeletePoint(pointId) {
      const index = this.maintenancePoints.findIndex(p => p.id === pointId);
      if (index !== -1) {
        this.maintenancePoints.splice(index, 1);
      }
      if (this.selectedPoint && this.selectedPoint.id === pointId) {
        this.selectedPoint = null;
      }
    },

    // 处理添加点位
    handleAddPoint(point) {
      // 生成唯一ID
      const newPoint = {
        ...point,
        id: Date.now()
      };
      this.maintenancePoints.push(newPoint);
      this.selectedPoint = newPoint;
    },

    // 处理保存
    handleSave() {
      // 这里可以调用API保存检修点配置
      this.$emit('save', this.maintenancePoints);
      this.$message.success('检修配置保存成功');
      this.$emit('update:visible', false);
    },

    // 处理取消
    handleCancel() {
      this.$emit('update:visible', false);
    },

    // 处理对话框关闭
    handleClosed() {
      this.maintenancePoints = [];
      this.selectedPoint = null;
    }
  }
};
</script>

<style lang="scss" scoped>
.maintenance-config-dialog {
  display: flex;
  flex-direction: column;

  ::v-deep .el-dialog {
    margin-top: 5vh !important;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    
    .el-dialog__body {
      flex: 1;
      overflow: hidden;
      padding: 10px;
    }
  }
}

.maintenance-config-container {
  display: flex;
  height: 70vh;
  min-height: 500px;
  
  .map-panel {
    flex: 3;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-right: 10px;
    overflow: hidden;
    background-color: #f5f7fa;
  }
  
  .point-list-panel {
    flex: 1;
    min-width: 300px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: auto;
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 10px;
}
</style>
