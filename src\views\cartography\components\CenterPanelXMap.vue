<template>
  <div class="center-panel-xmap">
    <div class="xmap-container">
      <!-- 没有合并SVG时显示空状态 -->
      <div class="placeholder-xmap" v-if="!mergeSvgUrl">
        <el-empty description="请先上传XMAP图层">
          <template #image>
            <el-icon size="64" color="var(--el-color-info)">
              <MapLocation />
            </el-icon>
          </template>
        </el-empty>
      </div>

      <!-- 有合并SVG时显示内容 -->
      <div class="xmap-content" v-else>
        <!-- 工具栏 -->
        <div class="xmap-toolbar">
          <div class="toolbar-left">
            <span class="toolbar-title">XMAP图层调整</span>
          </div>
          <div class="toolbar-right">
            <el-button-group size="small">
              <el-button @click="zoomOut" :disabled="viewState.scale <= zoomConfig.min">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="zoomIn" :disabled="viewState.scale >= zoomConfig.max">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
              <el-button @click="fitToContainer">
                <el-icon><FullScreen /></el-icon>
                适应窗口
              </el-button>
              <el-button @click="resetView">
                <el-icon><Refresh /></el-icon>
                重置视图
              </el-button>
              <el-button @click="saveCurrentOffset" type="primary" :loading="saving">
                <el-icon><Check /></el-icon>
                保存偏移
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 偏移控制面板 -->
        <div class="offset-controls">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="control-group">
                <label>X轴偏移 (pt):</label>
                <el-input-number
                  v-model="currentOffset.x"
                  :precision="2"
                  :step="transformStep.translate"
                  size="small"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="control-group">
                <label>Y轴偏移 (pt):</label>
                <el-input-number
                  v-model="currentOffset.y"
                  :precision="2"
                  :step="transformStep.translate"
                  size="small"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="control-group">
                <label>步长 (pt):</label>
                <el-input-number
                  v-model="transformStep.translate"
                  :precision="1"
                  :min="0.1"
                  :max="10"
                  size="small"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="control-group">
                <el-button-group size="small">
                  <el-button @click="moveLeft">←</el-button>
                  <el-button @click="moveUp">↑</el-button>
                  <el-button @click="moveDown">↓</el-button>
                  <el-button @click="moveRight">→</el-button>
                </el-button-group>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- SVG显示区域 -->
        <div
          class="svg-display"
          ref="svgDisplay"
          @wheel="handleWheel"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
          @mouseleave="handleMouseLeave"
        >
          <div class="svg-container" :style="containerTransform">
            <img
              ref="svgImage"
              :src="mergeSvgUrl"
              class="svg-image"
              :style="imageTransform"
              @load="onSvgLoad"
              @error="onSvgError"
              @dragstart.prevent
              alt="地图预览"
            />
            <div v-if="!mergeSvgUrl" class="no-image-placeholder">
              <el-icon size="48" color="var(--el-color-info)">
                <MapLocation />
              </el-icon>
              <p>暂无合并预览</p>
            </div>
          </div>

          <!-- 缩放信息显示 -->
          <div class="zoom-info" v-if="mergeSvgUrl">
            <span>{{ Math.round(viewState.scale * 100) }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="CenterPanelXMap">
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { MapLocation, Refresh, Check, ZoomIn, ZoomOut, FullScreen } from '@element-plus/icons-vue'
import { saveOffset as saveOffsetApi } from '@/api/dispatch/cartography'

// Props
const props = defineProps({
  currentMap: {
    type: Object,
    default: null
  },
  mergeSvgUrl: {
    type: String,
    default: ""
  },
  offset: {
    type: Object,
    required: true
  },
  transformStep: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update-current-map', 'update-offset'])

// Refs
const svgImage = ref(null)
const svgDisplay = ref(null)
const saving = ref(false)

// Data
const currentOffset = reactive({
  x: 0,
  y: 0
})

// 视图状态 - 用于缩放和拖动
const viewState = reactive({
  scale: 1,
  translateX: 0,
  translateY: 0
})

// 鼠标交互状态
const mouseState = reactive({
  isDragging: false,
  lastX: 0,
  lastY: 0,
  startX: 0,
  startY: 0
})

// 缩放配置
const zoomConfig = {
  min: 0.1,   // 最小缩放比例
  max: 5,     // 最大缩放比例
  step: 0.1   // 每次缩放步长
}

// 计算属性 - 容器变换样式
const containerTransform = computed(() => {
  return {
    transform: `translate(${viewState.translateX}px, ${viewState.translateY}px) scale(${viewState.scale})`,
    transformOrigin: 'center center',
    transition: mouseState.isDragging ? 'none' : 'transform 0.2s ease-out'
  }
})

// 计算属性 - 图片变换样式
const imageTransform = computed(() => {
  return {
    cursor: mouseState.isDragging ? 'grabbing' : 'grab',
    userSelect: 'none',
    pointerEvents: 'none'
  }
})

// Watchers
watch(() => props.offset, (newOffset) => {
  currentOffset.x = newOffset.x
  currentOffset.y = newOffset.y
}, { immediate: true, deep: true })

watch(() => props.mergeSvgUrl, (newUrl, oldUrl) => {
  console.log('mergeSvgUrl changed:', { oldUrl, newUrl })
})

// Lifecycle
onMounted(() => {
  console.log('CenterPanelXMap mounted')
})

onBeforeUnmount(() => {
  // 清理全局事件监听器
  document.removeEventListener('mousemove', handleGlobalMouseMove)
  document.removeEventListener('mouseup', handleGlobalMouseUp)
})

// Methods
const moveLeft = () => {
  currentOffset.x -= props.transformStep.translate
}

const moveRight = () => {
  currentOffset.x += props.transformStep.translate
}

const moveUp = () => {
  currentOffset.y -= props.transformStep.translate
}

const moveDown = () => {
  currentOffset.y += props.transformStep.translate
}

const resetView = () => {
  currentOffset.x = 0
  currentOffset.y = 0
  viewState.scale = 1
  viewState.translateX = 0
  viewState.translateY = 0
  ElMessage.success('视图已重置')
  saveCurrentOffset()
}

// 鼠标滚轮缩放处理
const handleWheel = (event) => {
  event.preventDefault()

  if (!svgDisplay.value || !mergeSvgUrl.value) return

  const rect = svgDisplay.value.getBoundingClientRect()
  const centerX = rect.width / 2
  const centerY = rect.height / 2

  // 计算鼠标相对于容器的位置
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top

  // 计算缩放前鼠标相对于图片中心的偏移
  const offsetX = (mouseX - centerX - viewState.translateX) / viewState.scale
  const offsetY = (mouseY - centerY - viewState.translateY) / viewState.scale

  // 计算新的缩放比例
  const delta = event.deltaY > 0 ? -zoomConfig.step : zoomConfig.step
  const newScale = Math.max(zoomConfig.min, Math.min(zoomConfig.max, viewState.scale + delta))

  if (newScale !== viewState.scale) {
    // 计算缩放后需要调整的平移量，使鼠标位置保持不变
    const newTranslateX = mouseX - centerX - offsetX * newScale
    const newTranslateY = mouseY - centerY - offsetY * newScale

    viewState.scale = newScale
    viewState.translateX = newTranslateX
    viewState.translateY = newTranslateY
  }
}

// 鼠标按下处理
const handleMouseDown = (event) => {
  if (event.button !== 0 || !mergeSvgUrl.value) return // 只处理左键

  event.preventDefault()
  mouseState.isDragging = true
  mouseState.startX = event.clientX
  mouseState.startY = event.clientY
  mouseState.lastX = event.clientX
  mouseState.lastY = event.clientY

  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleGlobalMouseMove)
  document.addEventListener('mouseup', handleGlobalMouseUp)
}

// 鼠标移动处理
const handleMouseMove = (event) => {
  if (!mouseState.isDragging) return

  const deltaX = event.clientX - mouseState.lastX
  const deltaY = event.clientY - mouseState.lastY

  viewState.translateX += deltaX
  viewState.translateY += deltaY

  mouseState.lastX = event.clientX
  mouseState.lastY = event.clientY
}

// 全局鼠标移动处理（用于拖拽时鼠标移出容器的情况）
const handleGlobalMouseMove = (event) => {
  if (!mouseState.isDragging) return

  const deltaX = event.clientX - mouseState.lastX
  const deltaY = event.clientY - mouseState.lastY

  viewState.translateX += deltaX
  viewState.translateY += deltaY

  mouseState.lastX = event.clientX
  mouseState.lastY = event.clientY
}

// 鼠标抬起处理
const handleMouseUp = () => {
  if (mouseState.isDragging) {
    mouseState.isDragging = false
    // 移除全局事件监听
    document.removeEventListener('mousemove', handleGlobalMouseMove)
    document.removeEventListener('mouseup', handleGlobalMouseUp)
  }
}

// 全局鼠标抬起处理
const handleGlobalMouseUp = () => {
  handleMouseUp()
}

// 鼠标离开处理
const handleMouseLeave = () => {
  // 不在这里结束拖拽，因为可能是拖拽到容器外部
}

const saveCurrentOffset = async () => {
  if (!props.currentMap || !props.currentMap.mapId) {
    ElMessage.error('当前地图信息无效，无法保存偏移')
    return
  }

  saving.value = true

  try {
    const offsetData = {
      mapId: props.currentMap.mapId,
      offsetX: currentOffset.x,
      offsetY: currentOffset.y
    }

    const response = await saveOffsetApi(offsetData)

    if (response.code === 200) {
      // 更新父组件的偏移量
      emit('update-offset', {
        x: currentOffset.x,
        y: currentOffset.y
      })

      if (currentOffset.x != 0 && currentOffset.y != 0)
        ElMessage.success('偏移量保存成功')

      // 通知父组件更新地图数据
      emit('update-current-map', {
        ...props.currentMap,
        mergeSvgUrl: response.msg,
        offsetX: currentOffset.x,
        offsetY: currentOffset.y,
      })
    } else {
      console.error('保存偏移量失败:', response.msg)
      ElMessage.error(response.msg || '保存偏移量失败')
      return
    }

  } catch (error) {
    console.error('保存偏移量失败:', error)
    ElMessage.error('保存偏移量失败')
  } finally {
    saving.value = false
  }
}

const onSvgLoad = () => {
  nextTick(() => {
    // SVG加载完成后的处理
    if (svgImage.value) {
      console.log('SVG图片已加载，尺寸:', svgImage.value.naturalWidth, 'x', svgImage.value.naturalHeight)
    }
  })
}

const onSvgError = (event) => {
  console.error('SVG图片加载失败:', props.mergeSvgUrl, event)
  ElMessage.error('地图预览加载失败，请检查图片链接')
}
</script>

<style lang="scss" scoped>
.center-panel-xmap {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.xmap-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.placeholder-xmap {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.xmap-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.xmap-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);

  .toolbar-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.offset-controls {
  padding: 16px;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-light);

  .control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;

    label {
      font-size: 12px;
      color: var(--el-text-color-regular);
      font-weight: 500;
    }

    .el-input-number {
      width: 100%;
    }

    .el-button-group {
      .el-button {
        padding: 6px 8px;
        font-size: 12px;
        min-width: 32px;
      }
    }
  }
}

.svg-display {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f5f7fa;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }

  .svg-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transform-origin: center center;

    .svg-image {
      max-width: none;
      max-height: none;
      width: auto;
      height: auto;
      object-fit: contain;
      background: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: none;
    }

    .no-image-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--el-text-color-secondary);

      p {
        margin: 8px 0 0 0;
        font-size: 14px;
      }
    }
  }

  .zoom-info {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    pointer-events: none;
    z-index: 10;

    span {
      font-family: 'Courier New', monospace;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .offset-controls {
    .el-col {
      margin-bottom: 12px;
    }
  }
}

@media (max-width: 768px) {
  .xmap-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;

    .toolbar-left,
    .toolbar-right {
      text-align: center;
    }
  }

  .offset-controls {
    .control-group {
      margin-bottom: 16px;
    }
  }
}
</style>
