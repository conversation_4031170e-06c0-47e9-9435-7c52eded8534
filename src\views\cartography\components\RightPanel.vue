<template>
  <div class="right-panel">
    <div class="panel-header">
      <h3>地图信息</h3>
    </div>
    
    <div class="panel-content">
      <!-- 地图基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">基本信息</span>
        </template>
        
        <div class="info-section" v-if="currentMap">
          <div class="info-item">
            <label>地图名称:</label>
            <span>{{ currentMap.name || '未命名' }}</span>
          </div>
          <div class="info-item">
            <label>比例尺:</label>
            <span>{{ currentMap.scale || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>备注:</label>
            <span>{{ currentMap.remark || '无' }}</span>
          </div>
        </div>
        
        <el-empty v-else description="暂无地图信息" :image-size="60" />
      </el-card>
      
      <!-- CAD图层信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <span class="card-title">CAD图层</span>
        </template>
        
        <div class="info-section" v-if="cadWidth && cadHeight">
          <div class="info-item">
            <label>图层尺寸:</label>
            <span>{{ cadWidth.toFixed(2) }} × {{ cadHeight.toFixed(2) }} pt</span>
          </div>
          <div class="info-item">
            <label>像素尺寸:</label>
            <span>{{ Math.round(cadWidth * 96/72) }} × {{ Math.round(cadHeight * 96/72) }} px</span>
          </div>
          <div class="info-item">
            <label>状态:</label>
            <el-tag type="success" size="small">已上传</el-tag>
          </div>
        </div>
        
        <div class="info-section" v-else>
          <div class="info-item">
            <label>状态:</label>
            <el-tag type="info" size="small">未上传</el-tag>
          </div>
        </div>
      </el-card>
      
      <!-- XMAP图层信息 -->
      <el-card class="info-card" shadow="never" v-if="currentMap">
        <template #header>
          <span class="card-title">XMAP图层</span>
        </template>
        
        <div class="info-section">
          <div class="info-item" v-if="currentMap.xmapWidth && currentMap.xmapHeight">
            <label>地图尺寸:</label>
            <span>{{ currentMap.xmapWidth.toFixed(2) }} × {{ currentMap.xmapHeight.toFixed(2) }} m</span>
          </div>
          <div class="info-item" v-if="currentMap.xmapVersion">
            <label>XMAP版本:</label>
            <span>{{ currentMap.xmapVersion }}</span>
          </div>
          <div class="info-item" v-if="currentMap.xmapSvgWidth && currentMap.xmapSvgHeight">
            <label>SVG尺寸:</label>
            <span>{{ currentMap.xmapSvgWidth.toFixed(2) }} × {{ currentMap.xmapSvgHeight.toFixed(2) }} px</span>
          </div>
          <div class="info-item" v-if="currentMap.minPosX !== undefined">
            <label>坐标范围:</label>
            <div class="coordinate-range">
              <div>X: {{ currentMap.minPosX }} ~ {{ currentMap.maxPosX }}</div>
              <div>Y: {{ currentMap.minPosY }} ~ {{ currentMap.maxPosY }}</div>
            </div>
          </div>
          <div class="info-item">
            <label>偏移量:</label>
            <div class="offset-info">
              <div>X: {{ currentMap.offset?.x || 0 }} pt</div>
              <div>Y: {{ currentMap.offset?.y || 0 }} pt</div>
            </div>
          </div>
          <div class="info-item">
            <label>状态:</label>
            <el-tag 
              :type="currentMap.mergeSvgUrl ? 'success' : 'info'" 
              size="small"
            >
              {{ currentMap.mergeSvgUrl ? '已上传' : '未上传' }}
            </el-tag>
          </div>
        </div>
      </el-card>
      
      <!-- 点位统计信息 -->
      <el-card class="info-card" shadow="never" v-if="currentMap">
        <template #header>
          <span class="card-title">点位统计</span>
        </template>
        
        <div class="info-section">
          <div class="info-item">
            <label>工作点位:</label>
            <span>{{ currentMap.advancedPoints?.length || 0 }} 个</span>
          </div>
          <div class="info-item">
            <label>路径数量:</label>
            <span>{{ currentMap.advancedRoutes?.length || 0 }} 条</span>
          </div>
          <div class="info-item">
            <label>障碍点位:</label>
            <span>{{ currentMap.obstacles?.length || 0 }} 个</span>
          </div>
        </div>
      </el-card>
      
      <!-- 操作提示 -->
      <el-card class="info-card tips-card" shadow="never">
        <template #header>
          <span class="card-title">操作提示</span>
        </template>
        
        <div class="tips-content">
          <div class="tip-item">
            <el-icon><InfoFilled /></el-icon>
            <span>请先上传CAD图层，再上传XMAP图层</span>
          </div>
          <div class="tip-item">
            <el-icon><InfoFilled /></el-icon>
            <span>在XMAP图层中可以调整偏移量</span>
          </div>
          <div class="tip-item">
            <el-icon><InfoFilled /></el-icon>
            <span>保存偏移后会重新生成合并预览</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="RightPanel">
import { InfoFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  currentMap: {
    type: Object,
    default: null
  },
  cadWidth: {
    type: Number,
    default: null
  },
  cadHeight: {
    type: Number,
    default: null
  }
})
</script>

<style lang="scss" scoped>
.right-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .card-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  :deep(.el-card__body) {
    padding: 16px;
  }
}

.info-section {
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    label {
      font-size: 13px;
      color: var(--el-text-color-regular);
      font-weight: 500;
      min-width: 80px;
      margin-right: 8px;
      flex-shrink: 0;
    }
    
    span {
      font-size: 13px;
      color: var(--el-text-color-primary);
      flex: 1;
      word-break: break-all;
    }
    
    .coordinate-range,
    .offset-info {
      font-size: 12px;
      color: var(--el-text-color-primary);
      
      div {
        margin-bottom: 2px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.tips-card {
  .tips-content {
    .tip-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .el-icon {
        color: var(--el-color-primary);
        margin-right: 8px;
        margin-top: 2px;
        flex-shrink: 0;
      }
      
      span {
        font-size: 13px;
        color: var(--el-text-color-regular);
        line-height: 1.4;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .panel-content {
    padding: 12px;
  }
  
  .info-card {
    margin-bottom: 12px;
    
    :deep(.el-card__body) {
      padding: 12px;
    }
  }
  
  .info-section {
    .info-item {
      flex-direction: column;
      align-items: flex-start;
      
      label {
        margin-bottom: 4px;
        margin-right: 0;
      }
    }
  }
}
</style>
