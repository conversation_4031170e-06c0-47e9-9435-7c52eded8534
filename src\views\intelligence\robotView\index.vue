<template>
  <div class="app-container">
    <el-row>
      <el-card class="box-card" shadow="hover" v-for="robot in robotList">
        <el-row>
          <el-col :span="12">
            <img src="/favicon.ico" class="card-image"/>
          </el-col>
          <el-col :span="12">
            <el-form>
              <el-form-item label="设备名称">
                <span>{{ robot.robotName }}</span>
              </el-form-item>
              <el-form-item label="序列号">
                <span> {{ robot.sn }}</span>
              </el-form-item>
              <el-form-item label="投运时间">
                <span>{{ formatDate(robot.beginUseTime) }}</span>
              </el-form-item>
              <el-form-item label="运行状态">
                <el-button type="text" @click="viewStatus(robot.robotDynamicAttr.id)">查看详情</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row>
          <el-form :inline="true">
            <el-form-item label="型号" label-position="right" style="text-align: right;">
              {{ robot.robotModel }}
            </el-form-item>
            <el-form-item label="状态" label-position="right" style="text-align: right;">
              <span v-html=" coverRobotStatus(robot.status)"></span>
            </el-form-item>
            <el-form-item label="运行里程" label-position="right" style="text-align: right;">
              <span>{{ coverm(robot.robotDynamicAttr.totalOdom) }}</span>
            </el-form-item>
            <el-form-item label="运行时间" label-position="right" style="text-align: right;">
              <span>{{ covers(robot.robotDynamicAttr.totalRunning) }}</span>
            </el-form-item>
          </el-form>
        </el-row>
      </el-card>
    </el-row>
  </div>

  <el-dialog  v-model="viewForm.open" width="200px" append-to-body>
    <el-form >
      <el-form-item label="当前位置" label-position="right" style="text-align: right;">
       暂无
      </el-form-item>
      <el-form-item label="移动速度" label-position="right" style="text-align: right;">
        暂无
      </el-form-item>
      <el-form-item label="电池电量" label-position="right" style="text-align: right;">
        暂无
      </el-form-item>
    </el-form>
  </el-dialog>

</template>

<script setup name="robotView">
import {list as initData} from "@/api/intelligence/robotView.js"

const robotList = ref([])
const {proxy} = getCurrentInstance()

const viewForm =reactive({
  open:false,
})


//获取列表
function getList() {
  initData({}).then(res => {
    robotList.value = res.data;
  })
}

function coverRobotStatus(status) {
  let statusName;
  status = Number(status);
  switch (status) {
    case 0:
      statusName = "<span style='color:#b4adad;'>待机</span>";
      break;
    case 1:
      statusName = "<span style='color:green;'>工作</span>";
      break;
    case 4:
      statusName = "<span style='color:#3c70ef;'>暂停</span>";
      break;
    case 6:
      statusName = "<span style='color:#3c70ef;'>定位中</span>";
      break;
    case 701:
      statusName = "<span style='color:red;'>载入地图失败</span>";
      break;
    case 702:
      statusName = "<span style='color:red;'>定位失败</span>";
      break;
    case 703:
      statusName = "<span style='color:red;'>导航失败</span>";
      break;
    case 8:
      statusName = "<span style='color:#b4adad;'>离线</span>";
      break;
    case 9:
      statusName = "<span style='color:red;'>故障</span>";
      break;
    default:
      statusName = '';
      break
  }
  console.log(status,statusName)
  return statusName;
}

function viewStatus(id){
  viewForm.open=true;

}


function covers(totalOdom) {
  const days = Math.floor(totalOdom / (24 * 3600));  // 计算天数
  totalOdom %= (24 * 3600);  // 获取剩余的秒数
  const hours = Math.floor(totalOdom / 3600);  // 计算小时数
  totalOdom %= 3600;  // 获取剩余的秒数
  const minutes = Math.floor(totalOdom / 60);  // 计算分钟数
  const remainingSeconds = totalOdom % 60;  // 剩余的秒数
  //`${days}天 ${hours}小时 ${minutes}分钟 ${remainingSeconds}秒`;
  return `${days}天 ${hours}小时`;
}

function coverm(totalOdom) {
  if (totalOdom < 1000) {
    return totalOdom + "m"
  } else {
    return (totalOdom / 1000).toFixed(2) + "km"
  }
}

function formatDate(date) {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  //${year}-${month}-${day} ${hours}:${minutes}:${seconds}
  return `${year}-${month}-${day}`;
}

getList();
</script>

<style scoped>
.box-card {
  width: 365px;
  margin: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-image {
  width: 140px;

}

.el-form-item--default {
  margin-bottom: 6px;
}


</style>