<template>
  <div class="center-panel">
    <div class="map-container">
      <!-- 没有选择地图或没有上传任何图层时显示空状态 -->
      <div class="placeholder-map" v-if="!currentMap || !cadLayerUrl">
        <el-empty description="请选择或新建地图"></el-empty>
      </div>
      <!-- 有地图且至少上传了一个图层时显示内容 -->
      <div class="map-content" v-else>
        <!-- 使用ECharts显示地图图层 -->
        <div ref="echartsContainer" class="echarts-container" style="border: 1px solid red;"></div>
      </div>
    </div>
  </div>
</template>

<script>
import AdvancedControls from './AdvancedControls.vue';
import * as echarts from 'echarts';

export default {
  name: "CenterPanel",
  components: {
    AdvancedControls
  },
  props: {
    currentMap: {
      type: Object,
      default: null
    },
    cadLayerUrl: {
      type: String,
      default: ""
    },
    activeLayer: {
      type: String,
      default: "center"
    },
    cadTransform: {
      type: Object,
      required: true
    },
  },
  watch: {
    // 监听图层URL变化，更新图表
    cadLayerUrl() {
      this.initEcharts();
    },
    // 监听变换属性变化，更新图表
    cadTransform: {
      handler() {
        this.updateEchartsLayers();
      },
      deep: true
    },
  },
  data() {
    return {
      echartsInstance: null,
      // 像素单位的尺寸变量
      cadWidthPx: 0,
      cadHeightPx: 0,
      // pt单位的尺寸变量
      cadWidth: 0,
      cadHeight: 0,
      // pt到px的转换比例
      pt2px: 96 / 72,
      // 基础缩放和位置
      baseScale: 1,
      baseX: 0,
      baseY: 0
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initEcharts();
    });
    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', this.resizeEcharts);
  },
  beforeDestroy() {
    // 移除事件监听和销毁图表实例
    window.removeEventListener('resize', this.resizeEcharts);
    if (this.echartsInstance) {
      this.echartsInstance.dispose();
    }
  },
  methods: {
    // 初始化ECharts实例
    initEcharts() {
      // 确保DOM已经渲染
      this.$nextTick(() => {
        // 如果已经有实例，先销毁
        if (this.echartsInstance) {
          this.echartsInstance.dispose();
        }

        // 如果没有图层，不初始化
        if (!this.cadLayerUrl) {
          return;
        }

        // 初始化ECharts实例
        this.echartsInstance = echarts.init(this.$refs.echartsContainer);

        // 预加载图片以获取尺寸
        this.preloadImages().then(() => {
          // 设置图表选项
          this.updateEchartsLayers();

          // 监听图表事件
          this.echartsInstance.on('click', this.handleChartClick);
        });
      });
    },

    // 预加载图片以获取尺寸
    preloadImages() {
      const promises = [];

      // 预加载CAD图层
      if (this.cadLayerUrl) {
        const cadPromise = new Promise((resolve) => {
          const img = new Image();
          img.onload = () => {
            // 保存图片像素尺寸
            this.cadWidthPx = img.width;
            this.cadHeightPx = img.height;
            console.log('CAD图层像素尺寸:', this.cadWidthPx, 'x', this.cadHeightPx);

            // 获取后端提供的pt单位尺寸
            const cadWidthPt = this.currentMap && this.currentMap.cadWidth;
            const cadHeightPt = this.currentMap && this.currentMap.cadHeight;

            if (cadWidthPt && cadHeightPt) {
              // 如果后端提供了pt单位尺寸，计算pt到px的转换比例
              this.pt2px = Math.max(
                this.cadWidthPx / cadWidthPt,
                this.cadHeightPx / cadHeightPt
              );
              console.log('计算得到的pt到px转换比例:', this.pt2px);

              // 保存pt单位尺寸
              this.cadWidth = cadWidthPt;
              this.cadHeight = cadHeightPt;
            } else {
              // 如果后端没有提供pt单位尺寸，使用标准转换比例
              this.pt2px = 96 / 72; // 标准转换比例：1pt = 1/72英寸，1px = 1/96英寸
              console.log('使用标准pt到px转换比例:', this.pt2px);

              // 从像素尺寸转换到pt
              this.cadWidth = this.cadWidthPx / this.pt2px;
              this.cadHeight = this.cadHeightPx / this.pt2px;
            }

            console.log('CAD图层pt单位尺寸:', this.cadWidth, 'x', this.cadHeight);
            resolve();
          };
          img.onerror = () => {
            console.error('CAD图层加载失败');
            resolve();
          };
          img.src = this.cadLayerUrl;
        });
        promises.push(cadPromise);
      }

      return Promise.all(promises);
    },

    // 更新ECharts图层
    updateEchartsLayers() {
      if (!this.echartsInstance) return;

      // 获取容器尺寸
      const containerWidth = this.$refs.echartsContainer.clientWidth;
      const containerHeight = this.$refs.echartsContainer.clientHeight;

      // 计算CAD图层的缩放比例，使其最大限度填充容器
      let cadScale = 1;
      if (this.cadWidthPx && this.cadHeightPx) {
        const scaleX = containerWidth / this.cadWidthPx;
        const scaleY = containerHeight / this.cadHeightPx;
        cadScale = Math.min(scaleX, scaleY); // 取较小值，确保完全显示
      }

      // 基础配置
      console.log('画布尺寸px:', containerWidth, 'x', containerHeight);
      const option = {
        backgroundColor: '#f5f7fa',
        animation: false,
        hoverLayerThreshold: Infinity,
        grid: {
          show: false,
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          containLabel: false
         },
        xAxis: {
          show: false,
          type: 'value',
          min: 0,
          max: containerWidth
        },
        yAxis: {
          show: false,
          type: 'value',
          min: 0,
          max: containerHeight
        },
        series: [],
        graphic: []
      };

      // 添加CAD图层
      if (this.cadLayerUrl) {
        const cadOpacity = this.activeLayer === 'cad' || this.activeLayer === 'center' ? 1 : 0.4;

        // 计算居中位置（px）
        const centeredX = (containerWidth - this.cadWidthPx * cadScale) / 2;
        const centeredY = (containerHeight - this.cadHeightPx * cadScale) / 2;

        console.log('CAD图层偏移画布px:', centeredX, centeredY);

        // 使用cadScale作为基础缩放，cadTransform.scale作为用户调整的缩放
        const finalScale = cadScale * this.cadTransform.scale;

        option.graphic.push({
          type: 'image',
          id: 'cadLayer',
          style: {
            image: this.cadLayerUrl,
            opacity: cadOpacity,
            width: this.cadWidthPx,
            height: this.cadHeightPx
          },
          position: [
            centeredX + this.cadTransform.translateX,
            centeredY + this.cadTransform.translateY
          ],
          rotation: this.cadTransform.rotation * Math.PI / 180,
          scale: [finalScale, finalScale],
          origin: [0, 0],
          z: 1,
          draggable: false
        });

        // 保存基础缩放和位置，供XMAP图层使用
        this.baseScale = cadScale;
        this.baseX = centeredX;
        this.baseY = centeredY;
      }

      // 设置图表选项
      this.echartsInstance.setOption(option, true);
    },

    // 调整图表大小
    resizeEcharts() {
      if (this.echartsInstance) {
        this.echartsInstance.resize();
        this.updateEchartsLayers();
      }
    },

    // 处理图表点击事件
    handleChartClick(params) {
      // 可以根据需要实现点击交互
      console.log('Chart clicked:', params);
    },

  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";
</style>





