<template>
	<div class="app-container">

		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button type="warning" plain @click="" :disabled="multiple">批量审核</el-button>
			</el-col>
		</el-row>

		<el-table v-loading="loading" :data="dataList" @selection-change="dataSelection">
			<el-table-column type="selection"></el-table-column>
			<el-table-column label="任务名称" align="center" prop="taskName" />
			<el-table-column label="所属部门" align="center" prop="deptName" />
			<el-table-column label="开始时间" align="center" prop="startTime">
				<template #default="scope">
					{{ parseTime(scope.row.startTime) }}
				</template>
			</el-table-column>
			<el-table-column label="执行时长" align="center" prop="duration" />
			<el-table-column label="任务状态" align="center">
				<template #default="scope">
					<dict-tag :options="task_instance_status" :value="scope.row.status" />
				</template>
			</el-table-column>
			<el-table-column label="终止原因" align="center" prop="terminationReason" />
			<el-table-column label="已巡检" align="center">
				<template #default="scope">
					{{ scope.row.pointHealthNum + scope.row.pointAlarmNum + scope.row.abnormalNum }}
				</template>
			</el-table-column>
			<el-table-column label="正常" align="center" prop="pointHealthNum" />
			<el-table-column label="告警" align="center" prop="pointAlarmNum" />
			<el-table-column label="不可达" align="center" prop="unreachableNum" />
			<el-table-column label="智能报警" align="center" prop="aiAlarmNum" />
			<el-table-column label="识别异常" align="center" prop="abnormalNum" />
			<el-table-column label="审核状态" align="center">
				<template #default="scope">
					<dict-tag :options="audit_status" :value="scope.row.auditStatus" />
				</template>
			</el-table-column>

			<el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
				<template #default="scope">
					<el-button link type="primary" @click="audit(scope.row)">审核</el-button>
					<router-link :to="{name: 'viewTheReport', query: { id: scope.row.id }}">
						<el-button link type="primary" plain @click="">查看</el-button>
					</router-link>
					<el-button link type="primary" @click="exportPdf(scope.row)">导出</el-button>
				</template>
			</el-table-column>
		</el-table>
		<pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
			v-model:limit="queryParams.pageSize" @pagination="getList" />

		<el-dialog :title="title" v-model="open" width="800px" append-to-body>
			<el-form ref="formRef" :model="form" label-position="top">
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item>
							<el-image style="width: 400px; height: 400px" :src="apiUrl + form[formNum].imgUrl"
								:zoom-rate="1.2" :preview-src-list="[apiUrl + form[formNum].imgUrl]" :max-scale="7"
								:min-scale="0.2" fit="cover" show-progress :initial-index="0" />
						</el-form-item>
						<el-form-item label="备注">
							<el-input v-model="form[formNum].remark" type="textarea"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="规则信息">
							<span>{{ form[formNum].ruleExpressionText }}</span>
						</el-form-item>
						<el-form-item>
							<div v-for="it in form[formNum].taskInstanceNodeResultList">
								<div v-if="it.dataType == 1">
									<span>{{ it.idenModelParamName }} &nbsp;</span>
									<span>{{ it.floatValue }}</span>
								</div>
								<div v-else>
									<span>{{ it.enumKey }} &nbsp;</span>
									<span>{{ it.enumValue }}</span>
								</div>

								<el-radio-group v-model="it.correctFlag">
									<el-radio v-for="item in correct_flag"
										:value="item.value">{{ item.label }}</el-radio>
								</el-radio-group>
								<br>

								<span>实际值</span>
								<el-input v-model="it.actualFloatValue" :disabled="it.correctFlag != 0"></el-input>
							</div>
						</el-form-item>
					</el-col>

				</el-row>

			</el-form>

			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" style="float: left;"
						@click="formNum == 0 ? '' : formNum --">上一项</el-button>
					<el-button type="primary" style="float: left;"
						@click="formNum + 1 == form.length ? '' : formNum ++">下一项</el-button>
					<el-button type="primary" @click="submitForm">保 存</el-button>
					<el-button @click="open = false">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script>
	import taskInstance from '@/api/task/taskInstance';

	export default {
		data() {
			return {
				apiUrl: import.meta.env.VITE_APP_BASE_API,
				correct_flag: getCurrentInstance().proxy.useDict("correct_flag").correct_flag,
				task_instance_status: getCurrentInstance().proxy.useDict("task_instance_status").task_instance_status,
				audit_status: getCurrentInstance().proxy.useDict("audit_status").audit_status,
				formNum: 0,
				open: false,
				title: '',
				dataList: [],
				loading: false,
				total: 0,
				form: {},
				queryParams: {
					pageNum: 1,
					pageSize: 10
				},
				rules: {},
				ids: [],
				single: true,
				multiple: true
			}
		},
		mounted() {
			this.getList()
		},
		methods: {
			getList() {
				this.loading = true
				taskInstance.getList(this.queryParams).then(res => {
					this.dataList = res.rows
					this.total = res.total
					this.loading = false
				})
			},
			viewTheReport(row) {
				
			},
			dataSelection(rows) {
				this.ids = rows.map(item => item.id)
				this.single = rows.length != 1
				this.multiple = !rows.length
			},
			audit(row) {
				let ids = row.id || this.ids
				taskInstance.auditData(ids).then(res => {
					this.form = res.data
					console.log(this.form)
					this.title = ids.length > 1 ? '批量审核' : '审核'
					this.open = true
				})
			},
			submitForm() {
				taskInstance.auditDataEdit(this.form).then(res=>{
					this.$modal.msgSuccess('保存成功')
					this.open = false
					this.getList()
				})
			},
			exportPdf(row) {
				taskInstance.exportPdf({id: row.id}).then(res=>{					
					const blob = new Blob([res]);
					const url = URL.createObjectURL(blob);					
					const link = document.createElement('a');
					link.href = url;
					link.download = row.taskName + '_报告.pdf';				
					link.click();
					URL.revokeObjectURL(url);
				})
			}
		}
	}
</script>

<style>

</style>