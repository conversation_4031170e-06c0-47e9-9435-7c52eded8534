import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/hyc";


// 查询识别类型
export function listIdenModelParamGroup(query) {
    return request({
      url: '/iden/model/param/group/list',
      method: 'get',
      params: query
    })
}




// 新增识别类型
export function addIdenModelParamGroup(data) {
    return request({
      url: '/iden/model/param/group/add',
      method: 'post',
      data: data
    })
  }
  


// 删除识别类型
export function delIdenModelParamGroup(id) {
    return request({
      url: '/iden/model/param/group/delete/' + id,
      method: 'delete'
    })
  }