<template>
  <div id="terminal"></div>
</template>

<script>
  import "./css/xterm.css";
  import webssh from "./js/webssh.js";
  import xterm from "./js/xterm.js";

  export default {
    data() {
      return {

      }
    },
    methods: {
      init(row) {
        this.openTerminal(row)
      },
      openTerminal(row) {
        let options = {
          operate: 'connect',
          host: row.host, //IP
          port: '22', //端口号
          username: row.userName, //用户名
          password: row.password //密码
        }
        var client = webssh.client;
        var term = xterm.terminal;
		
		term.cols = 97
		term.rows = 37
		term.cursorBlink = true
		term.cursorStyle = "block"
		term.scrollback = 800
		term.tabStopWidth = 8
		term.screenKeys = true
		
        term.on('data', function(data) {
          //键盘输入时的回调函数
          client.sendClientData(data);
        });
        term.open(document.getElementById('terminal'));
        //在页面上显示连接中...
        term.write('Connecting...');
        //执行连接操作
        client.connect({
          onError: function(error) {
            //连接失败回调
            term.write('Error: ' + error + '\r\n');
          },
          onConnect: function() {
            //连接成功回调
            client.sendInitData(options);
          },
          onClose: function() {
            //连接关闭回调
            term.write("\rconnection closed");
          },
          onData: function(data) {
            //收到数据时回调
            term.write(data);
          }
        });
      }
    },
  }
</script>

