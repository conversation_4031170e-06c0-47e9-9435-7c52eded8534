<template>
  <div class="app-container">
    <el-dialog title="规则列表" v-model="tableListOpen" width="820px" append-to-body>
      <el-form :inline="true">
        <el-form-item>
          <el-button type="primary" plain icon="Plus" @click="addRule">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="tableList" style="width: 100%;">
        <el-table-column label="序号" width="50" type="index" align="center">
          <template #default="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="报警等级" align="center" prop="ruleResult" :show-overflow-tooltip="true">
          <template #default="scope">
            <span>{{scope.row.ruleResult==1?'高级':scope.row.ruleResult==2?'中级':'低级'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="表达式" align="center" prop="expressionJson" :show-overflow-tooltip="true">
          <template #default="scope">
          <el-tag v-for="item in JSON.parse(scope.row.expressionJson)">{{ item.label }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="updateRule(scope.row)">编辑
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog :title="idenRuleExprForm.title" v-model="idenRuleExprForm.open" width="900px" append-to-body>
      <el-form ref="idenRuleExprFormRef" :model="idenRuleExprForm" label-width="80px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="报警等级">
              <el-select placeholder="报警等级" v-model="idenRuleExprForm.ruleResult">
                <el-option v-for="dict in rule_result" :key="dict.value" :label="dict.label"
                           :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="表达式符">
              <el-button plain v-for="item in symbol" @click="addExpr(1,item)">{{ item }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="参数">
              <el-select placeholder="参数" v-model="idenRuleExprForm.idenModelParamId" @change="paramChange">
                <el-option v-for="item in params" :label="item.paramName" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            &nbsp
            <el-button type="primary" plain @click="addExpr(2)">添加</el-button>
            &nbsp
            <el-button type="primary" plain @click="clearItem(1)">清空</el-button>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="5">
            <el-form-item label="限值类型">
              <el-select placeholder="限值类型" v-model="idenRuleExprForm.dataType" @change="dataTypeChange">
                <el-option v-for="dict in data_type" :key="dict.value" :label="dict.label"
                           :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>


          <el-col :span="5" v-if="idenRuleExprForm.dataType=='1'">
            <el-form-item label="值">
              <el-input v-model="idenRuleExprForm.dataTypeValue"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5" v-if="idenRuleExprForm.dataType=='2'">
            <el-form-item label="值">
              <el-select placeholder="枚举" v-model="idenRuleExprForm.dataTypeValue">
                <el-option v-for="item in idenRuleExprForm.enumList" :key="item" :label="item"
                           :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            &nbsp
            <el-button type="primary" plain @click="addExpr(3)">添加</el-button>
            &nbsp
            <el-button type="primary" plain @click="clearItem(2)">清空</el-button>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="24">
            <el-form-item label="表达式">
              <el-tag v-for="item in idenRuleExprForm.expressionJsonStr">{{ item.label }}</el-tag>
              &nbsp &nbsp
              <el-button type="primary" plain @click="clearItem(3)" v-if="idenRuleExprForm.expressionJsonStr.length>0">
                清空
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="subForm">确 定</el-button>
          <el-button @click="resetForm">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="IdenRuleExpr">
import {list as initData, getIdenParam, addIdenRuleExpr, updateIdenRuleExpr,removeIdenRuleExpr} from "@/api/operation/idenRuleExpr"

const {proxy} = getCurrentInstance()
const {rule_result} = proxy.useDict("rule_result")
const {data_type} = proxy.useDict("data_type")
const tableList = ref([])
const tableListOpen = ref(false)
const idenRuleId = ref("")
const params = ref([]);
const symbol = ref(['(', ')', '&&', '||', '!', '+', '-', '*', '/', '>', '>=', '<', '<=', '=='])
const idenRuleExprForm = reactive({
  open: false,
  title: '',
  id: '',
  idenRuleId: '',
  ruleResult: '',
  expressionJsonStr: [],
  expressionText: '',
  idenModelParamId: '',
  dataType: '',
  dataTypeValue: '',
  enumList: [],
})


const showTableList = (ruleId) => {
  idenRuleId.value = ruleId;
  tableListOpen.value = true;
  idenRuleExprForm.idenRuleId = ruleId;
  resetForm();
  getList();
}

defineExpose({showTableList})

/** 查询登录日志列表 */
function getList() {
  initData({idenRuleId: idenRuleId.value}).then(response => {
    tableList.value = response.data;
  })
}

function addRule() {
  idenRuleExprForm.open = true;
  idenRuleExprForm.title = '新增规则'
  getIdenParam({idenRuleId: idenRuleId.value}).then(res => {
    params.value = res.data;
  })
}

function updateRule(row) {
  idenRuleExprForm.open = true;
  idenRuleExprForm.title = '编辑规则'
  getIdenParam({idenRuleId: idenRuleId.value}).then(res => {
    params.value = res.data;
  })
  idenRuleExprForm.id = row.id;
  idenRuleExprForm.ruleResult = row.ruleResult;
  idenRuleExprForm.expressionJsonStr = JSON.parse(row.expressionJson)
}

function addExpr(type, value) {
  if (type == 1) { //符号
    let sym = {type: 1, label: value, value: value}
    idenRuleExprForm.expressionJsonStr.push(sym)
  }
  if (type == 2) { //参数
    let param = params.value.find(item => item.id == idenRuleExprForm.idenModelParamId);
    if (param == undefined) {
      proxy.$modal.msgError("请选择参数");
      return;
    }
    let par = {
      type: 2,
      label: param.paramName,
      value: idenRuleExprForm.idenModelParamId,
      idenTypeName: param.idenTypeName,
      paramName: param.paramName
    }
    idenRuleExprForm.expressionJsonStr.push(par)
  }
  if (type == 3) { //值
    let val;
    if (idenRuleExprForm.dataType == 1) {
      if (idenRuleExprForm.dataTypeValue == '') {
        proxy.$modal.msgError("请输入值");
        return;
      }
      val = {type: 3, label: idenRuleExprForm.dataTypeValue, value: idenRuleExprForm.dataTypeValue}
    } else {
      if (idenRuleExprForm.dataTypeValue == '') {
        proxy.$modal.msgError("请选择值");
        return;
      }
      val = {
        type: 3,
        label: "'" + idenRuleExprForm.dataTypeValue + "'",
        value: "'" + idenRuleExprForm.dataTypeValue + "'"
      }
    }
    idenRuleExprForm.expressionJsonStr.push(val)
  }
}

function paramChange() {
  if (idenRuleExprForm.paramValue !== '') {
    let param = params.value.find(item => item.id == idenRuleExprForm.idenModelParamId);
    idenRuleExprForm.enumList = param.valueScopeJson == '' ? [] : JSON.parse(param.valueScopeJson);
  } else {
    idenRuleExprForm.enumList = [];
  }
}

function dataTypeChange() {
  idenRuleExprForm.dataTypeValue = '';
}


function clearItem(type) {
  if (type == 1) {
    idenRuleExprForm.idenModelParamId = '';
    idenRuleExprForm.enumList = [];
    if (idenRuleExprForm.dataType == 2) {
      idenRuleExprForm.dataType = ''
      idenRuleExprForm.dataTypeValue = '';
    }
  } else if (type == 2) {
    idenRuleExprForm.dataType = ''
    idenRuleExprForm.dataTypeValue = '';
  } else {
    idenRuleExprForm.expressionJsonStr = [];
  }
}

function subForm() {
  if (idenRuleExprForm.expressionJsonStr.length == 0) {
    proxy.$modal.msgError("表达式不能为空");
    return;
  } else {
    idenRuleExprForm.expressionJson = JSON.stringify(idenRuleExprForm.expressionJsonStr)
  }
  if (idenRuleExprForm.id == '') {
    addIdenRuleExpr(idenRuleExprForm).then(res => {
      proxy.$modal.msgSuccess("新增成功")
      resetForm();
      getList();
    })
  } else {
    updateIdenRuleExpr(idenRuleExprForm).then(res => {
      proxy.$modal.msgSuccess("编辑成功")
      resetForm();
      getList();
    })
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除?').then(function () {
    return removeIdenRuleExpr(row)
  }).then(() => {
    console.log(row)
    resetForm();
    getList();
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {
  })
}

function resetForm() {
  idenRuleExprForm.open = false;
  idenRuleExprForm.title = '';
  idenRuleExprForm.id = '';
  idenRuleExprForm.idenRuleId = idenRuleId.value;
  idenRuleExprForm.ruleResult = '';
  idenRuleExprForm.expressionJsonStr = [];
  idenRuleExprForm.expressionJson = '';
  idenRuleExprForm.expressionText = '';
  idenRuleExprForm.idenModelParamId = '';
  idenRuleExprForm.dataType = '';
  idenRuleExprForm.dataTypeValue = '';
  idenRuleExprForm.enumList = [];
}
</script>
