<template>
  <div class="center-panel-main">
    <div class="map-container">
      <!-- 没有选择地图或没有上传任何图层时显示空状态 -->
      <div class="placeholder-map" v-if="!currentMap || !cadLayerUrl">
        <el-empty description="请选择或新建地图"></el-empty>
      </div>
      <!-- 有地图且上传了CAD图层时显示内容 -->
      <div class="map-content" v-else>
        <!-- 使用ECharts显示地图图层 -->
        <div ref="echartsContainer" class="echarts-container"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { calculateBezierPoints, calculateArcPoints } from '@/views/robot/panorama/utils/MapEchartsRenderer';

export default {
  name: "CenterPanelMain",
  props: {
    currentMap: {
      type: Object,
      default: null
    },
    cadLayerUrl: {
      type: String,
      default: ""
    },
    cadTransform: {
      type: Object,
      required: true
    },
    // 是否显示工作点位
    showWorkPoints: {
      type: Boolean,
      default: false
    },
    // 是否显示检修点位
    showObstaclePoints: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    // 监听图层URL变化，更新图表
    cadLayerUrl() {
      this.initEcharts();
    },
    // 监听变换属性变化，更新图表
    cadTransform: {
      handler() {
        this.updateEchartsLayers();
      },
      deep: true
    },
    // 监听currentMap变化
    currentMap: {
      handler() {
        this.updateMapElements();
      },
      deep: true
    },
    // 监听 showWorkPoints 变化
    showWorkPoints() {
      this.updateEchartsLayers();
    },
    // 监听 showObstaclePoints 变化
    showObstaclePoints() {
      this.updateEchartsLayers();
    }
  },
  data() {
    return {
      echartsInstance: null,
      // 像素单位的尺寸变量
      cadWidthPx: 0,
      cadHeightPx: 0,
      // pt单位的尺寸变量
      cadWidth: 0,
      cadHeight: 0,
      // pt到px的转换比例
      pt2px: 96 / 72,
      // 基础缩放和位置
      baseScale: 1,
      baseX: 0,
      baseY: 0,
      // 用户交互状态
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0,
      // 视图状态 - 用于记忆缩放和平移
      viewState: {
        scale: 1,
        translateX: 0,
        translateY: 0
      },
      // 缩放配置
      zoomConfig: {
        min: 0.2,  // 最小缩放比例
        max: 20,   // 最大缩放比例
        step: 0.1  // 每次缩放步长
      }
    };
  },
  mounted() {
    // 使用setTimeout确保DOM完全渲染后再初始化ECharts
    setTimeout(() => {
      this.initEcharts();
    }, 100);

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', this.resizeEcharts);
  },
  beforeDestroy() {
    // 移除事件监听和销毁图表实例
    window.removeEventListener('resize', this.resizeEcharts);

    // 清理鼠标事件监听
    this.cleanupMouseEvents();

    if (this.echartsInstance) {
      this.echartsInstance.dispose();
    }
  },
  methods: {
    // 初始化ECharts实例
    initEcharts() {
      console.log('CenterPanelMain.initEcharts 被调用');

      // 确保DOM已经渲染
      this.$nextTick(() => {
        try {
          // 如果已经有实例，先销毁
          if (this.echartsInstance) {
            console.log('销毁现有的ECharts实例');
            this.echartsInstance.dispose();
            this.echartsInstance = null;
          }

          // 如果没有图层，不初始化
          if (!this.cadLayerUrl) {
            console.log('没有CAD图层URL，不初始化ECharts');
            return;
          }

          // 检查容器是否存在
          if (!this.$refs.echartsContainer) {
            console.error('ECharts容器不存在，无法初始化');
            return;
          }

          // 检查容器尺寸
          const containerWidth = this.$refs.echartsContainer.clientWidth;
          const containerHeight = this.$refs.echartsContainer.clientHeight;

          console.log('ECharts容器尺寸:', containerWidth, 'x', containerHeight);

          if (containerWidth <= 0 || containerHeight <= 0) {
            console.error('ECharts容器尺寸为0，延迟初始化');
            // 延迟再次尝试初始化
            setTimeout(() => {
              this.initEcharts();
            }, 200);
            return;
          }

          // 初始化ECharts实例
          this.echartsInstance = echarts.init(this.$refs.echartsContainer);

          // 设置鼠标事件监听
          this.setupMouseEvents();

          // 预加载图片以获取尺寸
          this.preloadImages().then(() => {
            // 设置图表选项
            this.updateEchartsLayers();

            // 监听图表事件
            this.echartsInstance.on('click', this.handleChartClick);
          }).catch(error => {
            console.error('预加载图片失败:', error);
          });
        } catch (error) {
          console.error('初始化ECharts实例时出错:', error);
        }
      });
    },

    // 预加载图片以获取尺寸
    preloadImages() {
      const promises = [];

      // 预加载CAD图层
      if (this.cadLayerUrl) {
        const cadPromise = new Promise((resolve) => {
          const img = new Image();
          img.onload = () => {
            // 保存图片像素尺寸
            this.cadWidthPx = img.width;
            this.cadHeightPx = img.height;

            // 获取后端提供的pt单位尺寸
            const cadWidthPt = this.currentMap && this.currentMap.cadWidth;
            const cadHeightPt = this.currentMap && this.currentMap.cadHeight;

            if (cadWidthPt && cadHeightPt) {
              // 如果后端提供了pt单位尺寸，计算pt到px的转换比例
              this.pt2px = Math.max(
                this.cadWidthPx / cadWidthPt,
                this.cadHeightPx / cadHeightPt
              );

              // 保存pt单位尺寸
              this.cadWidth = cadWidthPt;
              this.cadHeight = cadHeightPt;
            } else {
              // 如果后端没有提供pt单位尺寸，使用标准转换比例
              this.pt2px = 96 / 72; // 标准转换比例：1pt = 1/72英寸，1px = 1/96英寸

              // 从像素尺寸转换到pt
              this.cadWidth = this.cadWidthPx / this.pt2px;
              this.cadHeight = this.cadHeightPx / this.pt2px;
            }

            resolve();
          };
          img.onerror = () => {
            console.error('CAD图层加载失败');
            resolve();
          };
          img.src = this.cadLayerUrl;
        });
        promises.push(cadPromise);
      }

      return Promise.all(promises);
    },

    // 更新ECharts图层
    updateEchartsLayers() {
      if (!this.echartsInstance) return;

      // 获取容器尺寸
      const containerWidth = this.$refs.echartsContainer.clientWidth;
      const containerHeight = this.$refs.echartsContainer.clientHeight;

      // 计算CAD图层的缩放比例，使其最大限度填充容器
      let cadScale = 1;
      if (this.cadWidthPx && this.cadHeightPx) {
        const scaleX = containerWidth / this.cadWidthPx;
        const scaleY = containerHeight / this.cadHeightPx;
        cadScale = Math.min(scaleX, scaleY); // 取较小值，确保完全显示
      }

      // 基础配置
      const option = {
        backgroundColor: '#f5f7fa',
        animation: false,
        hoverLayerThreshold: Infinity,
        grid: {
          show: false,
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          containLabel: false
        },
        xAxis: {
          show: false,
          type: 'value',
          min: 0,
          max: containerWidth
        },
        yAxis: {
          show: false,
          type: 'value',
          min: 0,
          max: containerHeight
        },
        tooltip: {
          trigger: 'item',
          confine: true,
          enterable: true,
          appendToBody: true,
          backgroundColor: 'rgba(50,50,50,0.9)',
          borderColor: '#333',
          borderWidth: 1,
          padding: [10, 15],
          textStyle: {
            color: '#fff'
          }
        },
        series: [],
        graphic: []
      };

      // 添加CAD图层
      if (this.cadLayerUrl) {
        // 计算居中位置（px）
        const centeredX = (containerWidth - this.cadWidthPx * cadScale) / 2;
        const centeredY = (containerHeight - this.cadHeightPx * cadScale) / 2;

        // 使用cadScale作为基础缩放，cadTransform.scale作为用户调整的缩放，viewState.scale作为交互缩放
        const finalScale = cadScale * this.cadTransform.scale * this.viewState.scale;

        // 计算最终位置，考虑基础位置、用户调整的位置和交互位置
        const finalX = centeredX + this.cadTransform.translateX + this.viewState.translateX;
        const finalY = centeredY + this.cadTransform.translateY + this.viewState.translateY;

        option.graphic.push({
          type: 'image',
          id: 'cadLayer',
          style: {
            image: this.cadLayerUrl,
            opacity: 1,
            width: this.cadWidthPx,
            height: this.cadHeightPx
          },
          position: [finalX, finalY],
          rotation: this.cadTransform.rotation * Math.PI / 180,
          scale: [finalScale, finalScale],
          origin: [0, 0],
          z: 1,
          draggable: false
        });

        // 保存基础缩放和位置，供点位使用
        this.baseScale = cadScale;
        this.baseX = centeredX;
        this.baseY = centeredY;
      }

      // 添加工作点位和路径
      this.addWorkPointsAndRoutes(option, containerWidth, containerHeight);

      // 设置图表选项
      this.echartsInstance.setOption(option, true);
    },

    // 添加工作点位和路径
    addWorkPointsAndRoutes(option, containerWidth, containerHeight) {
      // 添加工作点位 - 只在showWorkPoints为true时添加
      if (this.showWorkPoints && this.currentMap && this.currentMap.advancedPoints && this.currentMap.advancedPoints.length > 0) {
        // 创建散点图系列用于显示点位
        const pointData = [];

        // 使用CAD图层的坐标系，同时考虑交互状态
        const activeScale = this.baseScale * this.cadTransform.scale * this.viewState.scale;
        const activeTranslateX = this.baseX + this.cadTransform.translateX + this.viewState.translateX;
        const activeTranslateY = this.baseY + this.cadTransform.translateY + this.viewState.translateY;

        // 处理每个点位
        this.currentMap.advancedPoints.forEach((point, index) => {
          // 1. 首先将pt单位转换为px单位
          const pointXPx = point.newX * this.pt2px;
          const pointYPx = point.newY * this.pt2px;

          // 2. 然后考虑CAD图层的缩放
          const scaledX = pointXPx * activeScale;
          const scaledY = pointYPx * activeScale;

          // 3. 最后加上CAD图层的偏移量
          const x = activeTranslateX + scaledX;
          const y = activeTranslateY + scaledY;

          // 4. 因为echart是左下角原点，所以需要Y轴翻转
          const finalY = containerHeight - y;
          const finalX = x;

          const instanceName = point.instanceName || `点位${index + 1}`;
          const displayName = point.name || `${instanceName}`;

          // 保存点位的所有属性，用于鼠标悬浮提示
          pointData.push([
            finalX,
            finalY,
            displayName,
            point.id,
            point.angle,
            point.theta,
            point.newX,
            point.newY,
            point.instanceName,
            point.x,
            point.y,
          ]);
        });

        // 添加散点图系列
        option.series.push({
          type: 'scatter',
          id: 'workPoints',
          data: pointData,
          symbolSize: 20, // 点的大小
          symbol: 'pin', // 使用图钉形状，更加明显
          itemStyle: {
            color: '#FF5722', // 点的颜色
            borderColor: '#FFFFFF', // 边框颜色
            borderWidth: 2, // 边框宽度
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          },
          label: {
            show: true,
            position: 'top',
            distance: 5,
            formatter: '{@[2]}', // 显示点位名称
            backgroundColor: '#333',
            padding: [3, 5],
            borderRadius: 3,
            color: '#fff'
          },
          tooltip: {
            formatter: function(params) {
              return `
                <div style="font-weight:bold;margin-bottom:5px;">${params.data[2]}</div>
                <div>ID: ${params.data[3] || '无'}</div>
                <div>角度: ${params.data[4] || '无'}</div>
                <div>方向: ${params.data[5] || '无'}</div>
                <div>X坐标: ${params.data[9] || '无'}</div>
                <div>Y坐标: ${params.data[10] || '无'}</div>
                <div>点位名称: ${params.data[8] || '无'}</div>
              `;
            },
            backgroundColor: 'rgba(50,50,50,0.9)',
            borderColor: '#333',
            borderWidth: 1,
            padding: [10, 15],
            textStyle: {
              color: '#fff'
            }
          },
          zlevel: 10 // 确保点位显示在最上层
        });
      };

      // 添加检修点位
      console.log('CenterPanelMain.addWorkPointsAndRoutes - 添加检修点位', this.currentMap.obstacles);
      if (this.showObstaclePoints && this.currentMap && this.currentMap.obstacles && this.currentMap.obstacles.length > 0) {
        // 创建散点图系列用于显示点位
        const pointData = [];

        // 使用CAD图层的坐标系，同时考虑交互状态
        const activeScale = this.baseScale * this.cadTransform.scale * this.viewState.scale;
        const activeTranslateX = this.baseX + this.cadTransform.translateX + this.viewState.translateX;
        const activeTranslateY = this.baseY + this.cadTransform.translateY + this.viewState.translateY;

        // 处理每个点位
        this.currentMap.obstacles.forEach((point, index) => {
          // 1. 首先将pt单位转换为px单位
          const pointXPx = point.newPosX * this.pt2px;
          const pointYPx = point.newPosY * this.pt2px;

          // 2. 然后考虑CAD图层的缩放
          const scaledX = pointXPx * activeScale;
          const scaledY = pointYPx * activeScale;

          // 3. 最后加上CAD图层的偏移量
          const x = activeTranslateX + scaledX;
          const y = activeTranslateY + scaledY;

          // 4. 因为echart是左下角原点，所以需要Y轴翻转
          const finalY = containerHeight - y;
          const finalX = x;

          const instanceName = point.instanceName || `检修点${index + 1}`;
          const displayName = point.name || `${instanceName}`;

          // 保存点位的所有属性，用于鼠标悬浮提示
          pointData.push([
            finalX,
            finalY,
            displayName,
            point.id,
            point.angle,
            point.theta,
            point.newPosX,
            point.newPosY,
            point.instanceName
          ]);
        });

        // 添加散点图系列
        option.series.push({
          type: 'scatter',
          id: 'obstaclePoints',
          data: pointData,
          symbolSize: 20, // 点的大小
          symbol: 'path://M835.55 383.17l-15.77-15.77a20.83 20.83 0 0 0-29.46 0L602.87 554.87a62.5 62.5 0 0 0-11.79 15.77l-176.35-176.35a20.83 20.83 0 0 0-29.46 0l-15.77 15.77a20.83 20.83 0 0 0 0 29.46l176.35 176.35a62.5 62.5 0 0 0-15.77 11.79L343.62 814.13a20.83 20.83 0 0 0 0 29.46l15.77 15.77a20.83 20.83 0 0 0 29.46 0l186.46-186.46a62.5 62.5 0 1 0 88.39-88.39l171.85-171.85a20.83 20.83 0 0 0 0-29.46z', // 检修点使用扳手图标
          itemStyle: {
            color: '#FF5722', // 点的颜色
            borderColor: '#FFFFFF', // 边框颜色
            borderWidth: 2, // 边框宽度
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          },
          label: {
            show: true,
            position: 'top',
            distance: 5,
            formatter: '{@[2]}', // 显示点位名称
            backgroundColor: '#333',
            padding: [3, 5],
            borderRadius: 3,
            color: '#fff'
          },
          tooltip: {
            formatter: function(params) {
              return `
                <div style="font-weight:bold;margin-bottom:5px;">${params.data[2]}</div>
                <div>ID: ${params.data[3] || '无'}</div>
                <div>角度: ${params.data[4] || '无'}</div>
                <div>方向: ${params.data[5] || '无'}</div>
                <div>X坐标: ${params.data[9] || '无'}</div>
                <div>Y坐标: ${params.data[10] || '无'}</div>
                <div>点位名称: ${params.data[8] || '无'}</div>
              `;
            },
            backgroundColor: 'rgba(50,50,50,0.9)',
            borderColor: '#333',
            borderWidth: 1,
            padding: [10, 15],
            textStyle: {
              color: '#fff'
            }
          },
          zlevel: 10 // 确保点位显示在最上层
        });
      };

      // 处理路径信息
      this.addRoutes(option, containerWidth, containerHeight);
    },

    // 添加路径
    addRoutes(option, containerWidth, containerHeight) {
      if (this.currentMap && this.currentMap.advancedRoutes && this.currentMap.advancedRoutes.length > 0) {
        // 使用CAD图层的坐标系，同时考虑交互状态
        const activeScale = this.baseScale * this.cadTransform.scale * this.viewState.scale;
        const activeTranslateX = this.baseX + this.cadTransform.translateX + this.viewState.translateX;
        const activeTranslateY = this.baseY + this.cadTransform.translateY + this.viewState.translateY;

        // 遍历所有路径
        this.currentMap.advancedRoutes.forEach((route, index) => {
          // 1. 首先将pt单位转换为px单位
          const startXPx = route.startPos.newX * this.pt2px;
          const startYPx = route.startPos.newY * this.pt2px;
          const endXPx = route.endPos.newX * this.pt2px;
          const endYPx = route.endPos.newY * this.pt2px;
          const ctrlX1Px = route.ctrlPos1.newX * this.pt2px;
          const ctrlY1Px = route.ctrlPos1.newY * this.pt2px;
          const ctrlX2Px = route.ctrlPos2.newX * this.pt2px;
          const ctrlY2Px = route.ctrlPos2.newY * this.pt2px;

          // 2. 计算最终坐标
          const startX = activeTranslateX + startXPx * activeScale;
          const startY = containerHeight - (activeTranslateY + startYPx * activeScale);
          const endX = activeTranslateX + endXPx * activeScale;
          const endY = containerHeight - (activeTranslateY + endYPx * activeScale);
          const ctrlX1 = activeTranslateX + ctrlX1Px * activeScale;
          const ctrlY1 = containerHeight - (activeTranslateY + ctrlY1Px * activeScale);
          const ctrlX2 = activeTranslateX + ctrlX2Px * activeScale;
          const ctrlY2 = containerHeight - (activeTranslateY + ctrlY2Px * activeScale);

          // 3. 根据路径类型绘制不同的路径
          let graphicItem = null;

          switch (route.type) {
            case 'straight_line': // 直线
              graphicItem = {
                type: 'line',
                coordinateSystem: 'cartesian2d',
                data: [[startX, startY], [endX, endY]],
                lineStyle: {
                  stroke: '#4CAF50',
                  width: 3
                },
                z: 5
              };
              option.series.push(graphicItem);
              break;

            case 'bezier_curve': // 贝塞尔曲线
              const bezierPoints = calculateBezierPoints(
                [startX, startY],
                [ctrlX1, ctrlY1],
                [ctrlX2, ctrlY2],
                [endX, endY],
                100 // 生成100个点以获得平滑的曲线
              );

              graphicItem = {
                type: 'line',
                symbolSize: 1,
                symbol: 'bezierCurve',
                smooth: true,
                coordinateSystem: 'cartesian2d',
                data: bezierPoints,
                lineStyle: {
                  opacity: 0.8,
                  width: 3,
                  color: '#2196F3'
                },
              };
              option.series.push(graphicItem);

              break;

            case 'convex': // 凸弧线
            case 'concave_arc': // 凹弧线
              const p1 = [startX, startY];
              const p2 = [endX, endY];
              // 生成弧线点
              const arcPoints = calculateArcPoints(p1, p2, route.radian, true);

              graphicItem = {
                type: 'lines',
                coordinateSystem: 'cartesian2d',
                polyline: true,
                data: [{
                  coords: arcPoints,
                  lineStyle: {
                    width: 3,
                    color: '#4CAF50'
                  }
                }],
                effect: { show: false },
                lineStyle: {
                  opacity: 0.8,
                  width: 1,
                },
                z: 5,
              };

              option.series.push(graphicItem);
              break;

            default:
              console.warn(`未知的路径类型: ${route.type}`);
              break;
          }
        });
      }
    },

    // 调整图表大小
    resizeEcharts() {
      if (this.echartsInstance) {
        this.echartsInstance.resize();
        this.updateEchartsLayers();
      }
    },

    // 处理图表点击事件
    handleChartClick(params) {
      // 可以根据需要实现点击交互
      console.log('Chart clicked:', params);
    },

    // 设置鼠标事件监听
    setupMouseEvents() {
      if (!this.echartsInstance || !this.$refs.echartsContainer) return;

      const container = this.$refs.echartsContainer;

      // 鼠标滚轮事件 - 缩放
      container.addEventListener('wheel', this.handleMouseWheel);

      // 鼠标按下事件 - 开始拖动
      container.addEventListener('mousedown', this.handleMouseDown);

      // 鼠标移动事件 - 拖动中
      container.addEventListener('mousemove', this.handleMouseMove);

      // 鼠标松开事件 - 结束拖动
      container.addEventListener('mouseup', this.handleMouseUp);
      container.addEventListener('mouseleave', this.handleMouseUp);

      // 设置初始鼠标样式
      container.style.cursor = 'grab';

      console.log('已设置鼠标事件监听');
    },

    // 清理鼠标事件监听
    cleanupMouseEvents() {
      if (!this.$refs.echartsContainer) return;

      const container = this.$refs.echartsContainer;

      container.removeEventListener('wheel', this.handleMouseWheel);
      container.removeEventListener('mousedown', this.handleMouseDown);
      container.removeEventListener('mousemove', this.handleMouseMove);
      container.removeEventListener('mouseup', this.handleMouseUp);
      container.removeEventListener('mouseleave', this.handleMouseUp);

      console.log('已清理鼠标事件监听');
    },

    // 处理鼠标滚轮事件 - 缩放
    handleMouseWheel(event) {
      event.preventDefault();

      // 获取鼠标位置相对于容器的坐标
      const rect = this.$refs.echartsContainer.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      // 计算缩放方向和大小
      const delta = event.deltaY > 0 ? -this.zoomConfig.step : this.zoomConfig.step;
      const newScale = Math.max(
        this.zoomConfig.min,
        Math.min(this.zoomConfig.max, this.viewState.scale + delta)
      );

      // 如果缩放没有变化，不进行操作
      if (newScale === this.viewState.scale) return;

      // 计算缩放中心点相对于图像的位置
      const imageX = mouseX - this.viewState.translateX;
      const imageY = mouseY - this.viewState.translateY;

      // 计算新的平移量，保持鼠标位置不变
      const scaleFactor = newScale / this.viewState.scale;
      const newTranslateX = mouseX - imageX * scaleFactor;
      const newTranslateY = mouseY - imageY * scaleFactor;

      // 更新视图状态
      this.viewState = {
        scale: newScale,
        translateX: newTranslateX,
        translateY: newTranslateY
      };

      // 更新图层显示
      this.updateEchartsLayers();
    },

    // 处理鼠标按下事件 - 开始拖动
    handleMouseDown(event) {
      // 只响应左键
      if (event.button !== 0) return;

      this.isDragging = true;
      this.lastMouseX = event.clientX;
      this.lastMouseY = event.clientY;

      // 改变鼠标样式
      this.$refs.echartsContainer.style.cursor = 'grabbing';
    },

    // 处理鼠标移动事件 - 拖动中
    handleMouseMove(event) {
      if (!this.isDragging) return;

      // 计算鼠标移动距离
      const deltaX = event.clientX - this.lastMouseX;
      const deltaY = event.clientY - this.lastMouseY;

      // 更新鼠标位置
      this.lastMouseX = event.clientX;
      this.lastMouseY = event.clientY;

      // 更新视图状态
      this.viewState.translateX += deltaX;
      this.viewState.translateY += deltaY;

      // 更新图层显示
      this.updateEchartsLayers();
    },

    // 处理鼠标松开事件 - 结束拖动
    handleMouseUp() {
      if (!this.isDragging) return;

      this.isDragging = false;

      // 恢复鼠标样式
      this.$refs.echartsContainer.style.cursor = 'grab';
    },

    // 更新地图元素（点位和路径）
    updateMapElements() {
      console.log('CenterPanelMain.updateMapElements 被调用');

      // 检查是否有当前地图
      if (!this.currentMap) {
        console.log('没有当前地图信息，无法更新地图元素');
        return;
      }

      // 检查是否有CAD图层URL
      if (!this.cadLayerUrl) {
        console.log('没有CAD图层URL，无法更新地图元素');
        return;
      }

      // 重新渲染地图
      this.$nextTick(() => {
        // 确保容器已经渲染
        if (!this.$refs.echartsContainer) {
          console.error('ECharts容器不存在，延迟更新地图元素');
          // 延迟再次尝试更新
          setTimeout(() => {
            this.updateMapElements();
          }, 200);
          return;
        }

        // 检查容器尺寸
        const containerWidth = this.$refs.echartsContainer.clientWidth;
        const containerHeight = this.$refs.echartsContainer.clientHeight;

        console.log('ECharts容器尺寸:', containerWidth, 'x', containerHeight);

        if (containerWidth <= 0 || containerHeight <= 0) {
          console.error('ECharts容器尺寸为0，延迟更新地图元素');
          // 延迟再次尝试更新
          setTimeout(() => {
            this.updateMapElements();
          }, 200);
          return;
        }

        if (this.echartsInstance) {
          console.log('使用现有ECharts实例更新图层');
          this.updateEchartsLayers();
        } else {
          console.log('初始化新的ECharts实例');
          this.initEcharts();
        }
      });
    },

  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";

.center-panel-main {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .map-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #f5f7fa;
    border-radius: 4px;
    min-height: 400px; /* 确保最小高度 */
    min-width: 400px; /* 确保最小宽度 */

    .placeholder-map {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
    }

    .map-content {
      height: 100%;
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .echarts-container {
        height: 100%;
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        cursor: grab; /* 默认显示抓取手势 */

        &:active {
          cursor: grabbing; /* 激活时显示抓取中手势 */
        }
      }
    }
  }
}
</style>
