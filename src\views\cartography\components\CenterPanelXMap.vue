<template>
  <div class="center-panel-xmap">
    <div class="xmap-container">
      <!-- 没有合并SVG时显示空状态 -->
      <div class="placeholder-xmap" v-if="!mergeSvgUrl">
        <el-empty description="请先上传XMAP图层">
          <template #image>
            <el-icon size="64" color="var(--el-color-info)">
              <MapLocation />
            </el-icon>
          </template>
        </el-empty>
      </div>
      
      <!-- 有合并SVG时显示内容 -->
      <div class="xmap-content" v-else>
        <!-- 工具栏 -->
        <div class="xmap-toolbar">
          <div class="toolbar-left">
            <span class="toolbar-title">XMAP图层调整</span>
          </div>
          <div class="toolbar-right">
            <el-button-group size="small">
              <el-button @click="resetView">
                <el-icon><Refresh /></el-icon>
                重置视图
              </el-button>
              <el-button @click="saveCurrentOffset" type="primary" :loading="saving">
                <el-icon><Check /></el-icon>
                保存偏移
              </el-button>
            </el-button-group>
          </div>
        </div>
        
        <!-- 偏移控制面板 -->
        <div class="offset-controls">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="control-group">
                <label>X轴偏移 (pt):</label>
                <el-input-number
                  v-model="currentOffset.x"
                  :precision="2"
                  :step="transformStep.translate"
                  size="small"
                  @change="updateOffsetDisplay"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="control-group">
                <label>Y轴偏移 (pt):</label>
                <el-input-number
                  v-model="currentOffset.y"
                  :precision="2"
                  :step="transformStep.translate"
                  size="small"
                  @change="updateOffsetDisplay"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="control-group">
                <label>步长 (pt):</label>
                <el-input-number
                  v-model="transformStep.translate"
                  :precision="1"
                  :min="0.1"
                  :max="10"
                  size="small"
                />
              </div>
            </el-col>
            <el-col :span="6">
              <div class="control-group">
                <el-button-group size="small">
                  <el-button @click="moveLeft">←</el-button>
                  <el-button @click="moveUp">↑</el-button>
                  <el-button @click="moveDown">↓</el-button>
                  <el-button @click="moveRight">→</el-button>
                </el-button-group>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- SVG显示区域 -->
        <div class="svg-display">
          <iframe 
            ref="svgFrame"
            :src="mergeSvgUrl" 
            class="svg-frame"
            frameborder="0"
            @load="onSvgLoad"
          ></iframe>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="CenterPanelXMap">
import { ref, reactive, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { MapLocation, Refresh, Check } from '@element-plus/icons-vue'
import { saveOffset as saveOffsetApi } from '@/api/dispatch/cartography'

// Props
const props = defineProps({
  currentMap: {
    type: Object,
    default: null
  },
  mergeSvgUrl: {
    type: String,
    default: ""
  },
  offset: {
    type: Object,
    required: true
  },
  transformStep: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update-current-map', 'update-offset'])

// Refs
const svgFrame = ref(null)
const saving = ref(false)

// Data
const currentOffset = reactive({
  x: 0,
  y: 0
})

// Watchers
watch(() => props.offset, (newOffset) => {
  currentOffset.x = newOffset.x
  currentOffset.y = newOffset.y
}, { immediate: true, deep: true })

watch(() => props.mergeSvgUrl, () => {
  console.log('mergeSvgUrl changed:', props.mergeSvgUrl)
})

// Lifecycle
onMounted(() => {
  console.log('CenterPanelXMap mounted')
})

// Methods
const updateOffsetDisplay = () => {
  // 实时更新偏移显示
  emit('update-offset', {
    x: currentOffset.x,
    y: currentOffset.y
  })
}

const moveLeft = () => {
  currentOffset.x -= props.transformStep.translate
  updateOffsetDisplay()
}

const moveRight = () => {
  currentOffset.x += props.transformStep.translate
  updateOffsetDisplay()
}

const moveUp = () => {
  currentOffset.y -= props.transformStep.translate
  updateOffsetDisplay()
}

const moveDown = () => {
  currentOffset.y += props.transformStep.translate
  updateOffsetDisplay()
}

const resetView = () => {
  currentOffset.x = 0
  currentOffset.y = 0
  updateOffsetDisplay()
  ElMessage.success('视图已重置')
}

const saveCurrentOffset = async () => {
  if (!props.currentMap || !props.currentMap.id) {
    ElMessage.error('当前地图信息无效，无法保存偏移')
    return
  }

  saving.value = true
  
  try {
    const offsetData = {
      mapId: props.currentMap.id,
      offsetX: currentOffset.x,
      offsetY: currentOffset.y
    }
    
    console.log('保存偏移量:', offsetData)
    
    await saveOffsetApi(offsetData)
    
    // 更新父组件的偏移量
    emit('update-offset', {
      x: currentOffset.x,
      y: currentOffset.y
    })
    
    ElMessage.success('偏移量保存成功')
    
    // 通知父组件更新地图数据
    emit('update-current-map', {
      ...props.currentMap,
      offset: {
        x: currentOffset.x,
        y: currentOffset.y
      }
    })
    
  } catch (error) {
    console.error('保存偏移量失败:', error)
    ElMessage.error('保存偏移量失败')
  } finally {
    saving.value = false
  }
}

const onSvgLoad = () => {
  console.log('SVG加载完成')
  nextTick(() => {
    // SVG加载完成后的处理
    if (svgFrame.value) {
      console.log('SVG iframe已加载')
    }
  })
}
</script>

<style lang="scss" scoped>
.center-panel-xmap {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.xmap-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.placeholder-xmap {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.xmap-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.xmap-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  
  .toolbar-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.offset-controls {
  padding: 16px;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-light);
  
  .control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    label {
      font-size: 12px;
      color: var(--el-text-color-regular);
      font-weight: 500;
    }
    
    .el-input-number {
      width: 100%;
    }
    
    .el-button-group {
      .el-button {
        padding: 6px 8px;
        font-size: 12px;
        min-width: 32px;
      }
    }
  }
}

.svg-display {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f5f7fa;
  
  .svg-frame {
    width: 100%;
    height: 100%;
    border: none;
    background: white;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .offset-controls {
    .el-col {
      margin-bottom: 12px;
    }
  }
}

@media (max-width: 768px) {
  .xmap-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    
    .toolbar-left,
    .toolbar-right {
      text-align: center;
    }
  }
  
  .offset-controls {
    .control-group {
      margin-bottom: 16px;
    }
  }
}
</style>
