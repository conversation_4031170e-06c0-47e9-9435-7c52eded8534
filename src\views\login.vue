<template>
	<div class="login-box" id="demo">
		<div class="input-content">
			<div class="login_tit">
				<div>
					<i class="tit-bg left"></i>
					{{ title }}
					<i class="tit-bg right"></i>
				</div>
				<p>Management System</p>
			</div>
			<p class="p user_icon">
				<input v-model="loginForm.username" type="text" placeholder="账号" autocomplete="off" class="login_txtbx">
			</p>
			<p class="p pwd_icon">
				<input v-model="loginForm.password" type="password" placeholder="密码" autocomplete="off" @keyup.enter="handleLogin"
					class="login_txtbx">
			</p>
			<div class="p val_icon" v-if="captchaEnabled">
				<div class="checkcode">
					<input v-model="loginForm.code" type="text" id="J_codetext" placeholder="验证码" autocomplete="off"
						class="login_txtbx">
					<div class="login-code">
						<img :src="codeUrl" @click="getCode" class="login-code-img" />
					</div>
				</div>
			</div>
			<div class="signup">
				<a class="gv" href="#" @click.prevent="handleLogin">登&nbsp;&nbsp;录</a>
			</div>
		</div>
		<div class="canvaszz"> </div>
		<canvas id="canvas"></canvas>
		<div class="el-login-footer">
			<span>Copyright © 2018-2025 Hyc All Rights Reserved.</span>
		</div>
	</div>
</template>

<script setup>
	import {
		getCodeImg
	} from "@/api/login"
	import Cookies from "js-cookie"
	import {
		encrypt,
		decrypt
	} from "@/utils/jsencrypt"
	import useUserStore from '@/store/modules/user'
	import {
		onMounted
	} from "vue"

	const title = import.meta.env.VITE_APP_TITLE
	const userStore = useUserStore()
	const route = useRoute()
	const router = useRouter()
	const {
		proxy
	} = getCurrentInstance()

	const loginForm = ref({
		username: "",
		password: "",
		rememberMe: false,
		code: "",
		uuid: ""
	})

	const loginRules = {
		username: [{
			required: true,
			trigger: "blur",
			message: "请输入您的账号"
		}],
		password: [{
			required: true,
			trigger: "blur",
			message: "请输入您的密码"
		}],
		code: [{
			required: true,
			trigger: "change",
			message: "请输入验证码"
		}]
	}

	const codeUrl = ref("")
	const loading = ref(false)
	// 验证码开关
	const captchaEnabled = ref(false)
	// 注册开关
	const register = ref(false)
	const redirect = ref(undefined)

	watch(route, (newRoute) => {
		redirect.value = newRoute.query && newRoute.query.redirect
	}, {
		immediate: true
	})

	function handleLogin() {
		if (!loginForm.value.username || !loginForm.value.password) {
			return proxy.$modal.msgError("账号密码不能为空")
		}
		loading.value = true
		// 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
		if (loginForm.value.rememberMe) {
			Cookies.set("username", loginForm.value.username, {
				expires: 30
			})
			Cookies.set("password", encrypt(loginForm.value.password), {
				expires: 30
			})
			Cookies.set("rememberMe", loginForm.value.rememberMe, {
				expires: 30
			})
		} else {
			// 否则移除
			Cookies.remove("username")
			Cookies.remove("password")
			Cookies.remove("rememberMe")
		}
		// 调用action的登录方法
		userStore.login(loginForm.value).then(() => {
			const query = route.query
			const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
				if (cur !== "redirect") {
					acc[cur] = query[cur]
				}
				return acc
			}, {})
			router.push({
				path: redirect.value || "/",
				query: otherQueryParams
			})
		}).catch(() => {
			loading.value = false
			// 重新获取验证码
			if (captchaEnabled.value) {
				getCode()
			}
		})
	}

	function getCode() {
		getCodeImg().then(res => {
			captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
			if (captchaEnabled.value) {
				codeUrl.value = "data:image/gif;base64," + res.img
				loginForm.value.uuid = res.uuid
			}
		})
	}

	function getCookie() {
		const username = Cookies.get("username")
		const password = Cookies.get("password")
		const rememberMe = Cookies.get("rememberMe")
		loginForm.value = {
			username: username === undefined ? loginForm.value.username : username,
			password: password === undefined ? loginForm.value.password : decrypt(password),
			rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
		}
	}

	getCode()
	getCookie()

	onMounted(() => {
		var canvas = document.getElementById('canvas'),
			ctx = canvas.getContext('2d'),
			w = canvas.width = window.innerWidth,
			h = canvas.height = window.innerHeight,

			hue = 217,
			stars = [],
			count = 0,
			maxStars = 2500; //星星数量

		var canvas2 = document.createElement('canvas'),
			ctx2 = canvas2.getContext('2d');
		canvas2.width = 100;
		canvas2.height = 100;
		var half = canvas2.width / 2,
			gradient2 = ctx2.createRadialGradient(half, half, 0, half, half, half);
		gradient2.addColorStop(0.025, '#CCC');
		gradient2.addColorStop(0.1, 'hsl(' + hue + ', 61%, 33%)');
		gradient2.addColorStop(0.25, 'hsl(' + hue + ', 64%, 6%)');
		gradient2.addColorStop(1, 'transparent');

		ctx2.fillStyle = gradient2;
		ctx2.beginPath();
		ctx2.arc(half, half, half, 0, Math.PI * 2);
		ctx2.fill();

		// End cache

		function random(min, max) {
			if (arguments.length < 2) {
				max = min;
				min = 0;
			}

			if (min > max) {
				var hold = max;
				max = min;
				min = hold;
			}

			return Math.floor(Math.random() * (max - min + 1)) + min;
		}

		function maxOrbit(x, y) {
			var max = Math.max(x, y),
				diameter = Math.round(Math.sqrt(max * max + max * max));
			return diameter / 2;
			//星星移动范围，值越大范围越小，
		}

		var Star = function() {

			this.orbitRadius = random(maxOrbit(w, h));
			this.radius = random(60, this.orbitRadius) / 18;
			//星星大小
			this.orbitX = w / 2;
			this.orbitY = h / 2;
			this.timePassed = random(0, maxStars);
			this.speed = random(this.orbitRadius) / 500000;
			//星星移动速度
			this.alpha = random(2, 10) / 10;

			count++;
			stars[count] = this;
		}

		Star.prototype.draw = function() {
			var x = Math.sin(this.timePassed) * this.orbitRadius + this.orbitX,
				y = Math.cos(this.timePassed) * this.orbitRadius + this.orbitY,
				twinkle = random(10);

			if (twinkle === 1 && this.alpha > 0) {
				this.alpha -= 0.05;
			} else if (twinkle === 2 && this.alpha < 1) {
				this.alpha += 0.05;
			}

			ctx.globalAlpha = this.alpha;
			ctx.drawImage(canvas2, x - this.radius / 2, y - this.radius / 2, this.radius, this.radius);
			this.timePassed += this.speed;
		}

		for (var i = 0; i < maxStars; i++) {
			new Star();
		}

		function animation() {
			ctx.globalCompositeOperation = 'source-over';
			ctx.globalAlpha = 0.5; //尾巴
			ctx.fillStyle = 'hsla(' + hue + ', 64%, 6%, 2)';
			ctx.fillRect(0, 0, w, h)

			ctx.globalCompositeOperation = 'lighter';
			for (var i = 1, l = stars.length; i < l; i++) {
				stars[i].draw();
			};

			window.requestAnimationFrame(animation);
		}

		animation();
	})
</script>

<style lang='scss' scoped>
	.el-login-footer {
		height: 40px;
		line-height: 40px;
		position: fixed;
		bottom: 0;
		width: 100%;
		text-align: center;
		color: #fff;
		font-family: Arial;
		font-size: 12px;
		letter-spacing: 1px;
	}

	.login-code {
		position: absolute;
		/* width: 50%; */
		height: 33px;
		left: 45%;

	}

	body {
		margin: 0 auto;
		overflow: hidden;
	}

	.login-box {
		margin: 0 auto;
		width: 100%;
		height: 100%;
		background-color: #000;
		position: relative;
	}

	.login-box canvas {
		width: 100%;
		height: auto;
		display: inline-block;
		vertical-align: baseline;
		position: absolute;
		/* z-index: -1; */
	}

	.login-box .canvaszz {
		width: 100%;
		background-image: url(../assets/login/in_top_bj.png);
		height: 800px;
		position: absolute;
		z-index: 10;
		filter: alpha(opacity=40);
		-moz-opacity: 0.4;
		-khtml-opacity: 0.4;
		opacity: 0.4;
	}

	.login_tit {
		position: absolute;
		top: -60px;
		left: -5px;
		width: 420px;
		color: #fff;
		text-align: center;

	}

	.tit-bg {
		position: absolute;
		top: 50%;
		display: inline-block;
		width: 90px;
		height: 1px;
		background: url(../assets/login/login-tit.png)
	}

	.tit-bg.left {
		left: 0;
		transform: rotate(180deg)
	}

	.tit-bg.right {
		right: 0
	}

	.login_tit>div {
		position: relative;
		font-size: 22px;
		font-weight: bold;
	}

	.login_tit>p {
		font-size: 18px;
		font-family: "arial";
		margin: 10px 0;
	}

	.login-box .signup {
		margin-top: 40px;
		text-align: center
	}

	.login-box .signup a.gv {
		text-decoration: none;
		background: url(../assets/login/nav_gv.png) repeat 0px 0px;
		width: 130px;
		height: 43px;
		display: inline-block;
		text-align: center;
		line-height: 43px;
		cursor: pointer;
		margin: 8px 20px 8px 10px;
		font: 18px/43px 'microsoft yahei';
		color: #066197;
	}

	.login-box .signup a.gv span {
		display: none;

	}

	.login-box .signup a.gv:hover {
		background: url(../assets/login/nav_gv.png) repeat 0px -43px;
		color: #1d7eb8;
		-webkit-box-shadow: 0 0 6px #1d7eb8;
		transition-duration: 0.5s;
	}

	.login-box .topcn {
		width: 980px;
		top: 200px;
		left: 50%;
		margin-left: -490px;
		position: absolute;
		z-index: 20;

	}

	.input-content {
		position: absolute;
		z-index: 9999;
		padding: 30px;
		/* width: 350px; */
		left: 50%;
		margin-left: -205px;
		top: 25%
	}

	.input-content .p {
		margin: 10px 0;
		height: 44px;
		position: relative;
	}

	.input-content .p .login_txtbx {
		font-size: 14px;
		/* height: 26px; */
		line-height: 26px;
		padding: 8px 9%;
		width: 350px;
		text-indent: 1em;
		border: 1px solid #1d7eb8;
		background: rgba(0, 0, 0, 0.1);
		color: white;
	}

	.login_txtbx::-webkit-input-placeholder {
		color: rgba(255, 255, 255, 0.9);
	}

	.login_txtbx:-moz-placeholder {
		color: rgba(255, 255, 255, 0.9);
	}

	.login_txtbx::-moz-placeholder {
		color: rgba(255, 255, 255, 0.9);
	}

	.login_txtbx:-ms-input-placeholder {
		color: rgba(255, 255, 255, 0.9);
	}

	.input-content .p .login_txtbx:focus,
	.input-content .p .login_txtbx:hover {
		-webkit-box-shadow: 0 0 6px #1d7eb8;
		transition-duration: 0.5s;
	}

	.input-content .p.user_icon:before {
		content: "";
	}

	.input-content .p.pwd_icon:before {
		content: "";
	}

	.input-content .p.opwd_icon:before {
		content: "";
	}

	.input-content .p.npwd_icon:before {
		content: "";
	}

	.input-content .p.rpwd_icon:before {
		content: "";
	}

	.input-content .p.val_icon:before {
		content: "";
	}

	.input-content .p:before {
		font-family: 'adminthemesregular';
		position: absolute;
		top: 0;
		left: 14px;
		height: 42px;
		line-height: 42px;
		font-size: 20px;
		color: #fff;
	}

	.input-content .p .checkcode {
		float: left;
		width: 205px;
		height: 44px;
		overflow: hidden;
	}

	.input-content .p .checkcode input {
		float: left;
		width: 120px;
		/* height: 36px; */
		line-height: 36px;
		border: 1px solid #1d7eb8;
		padding: 3px;
		color: white;
		outline: none;
		text-indent: 2.6em;
	}

	.input-content .p .checkcode canvas {
		float: right;
		width: 85px;
		height: 38px;
		padding: 3px;
		z-index: 0;
		background: rgba(28, 122, 178, 0.3);
	}

	.input-content .p .ver_btn {
		color: #f4f4f4;
		height: 42px;
		line-height: 42px;
		margin: 0;
		z-index: 1;
		position: relative;
		float: right;
		cursor: pointer;
		font-size: 14px;
	}

	.passwordTit {
		position: relative;
		padding: 10px;
		font-size: 12px;
		color: #fff;
		line-height: 18px;
		margin-bottom: -20px;
	}

	.passwordTit .border {
		position: absolute;
		display: inline-block;
		width: 20px;
		height: 20px;
		border: 1px solid #1d7eb8
	}

	.border.left {
		left: 0;
		top: 0;
		border-right: none;
		border-bottom: none
	}

	.border.right {
		right: 0;
		bottom: 0;
		border-left: none;
		border-top: none
	}
</style>