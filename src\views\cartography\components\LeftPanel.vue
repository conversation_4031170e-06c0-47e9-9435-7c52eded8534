<template>
  <div class="left-panel">
    <div class="panel-header">
      <h3>地图图层</h3>
    </div>
    
    <div class="panel-content">
      <el-tree
        ref="treeRef"
        :data="mapTreeData"
        :props="defaultProps"
        :expand-on-click-node="false"
        :default-expanded-keys="expandedKeys"
        node-key="id"
        highlight-current
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div class="tree-node">
            <el-icon v-if="data.type === 'cad'" class="node-icon">
              <Document />
            </el-icon>
            <el-icon v-else-if="data.type === 'xmap'" class="node-icon">
              <MapLocation />
            </el-icon>
            <el-icon v-else class="node-icon">
              <Folder />
            </el-icon>
            <span class="node-label">{{ node.label }}</span>
            <div class="node-status" v-if="data.type">
              <el-tag 
                :type="getLayerStatus(data.type)" 
                size="small"
              >
                {{ getLayerStatusText(data.type) }}
              </el-tag>
            </div>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup name="LeftPanel">
import { ref, computed } from 'vue'
import { Document, MapLocation, Folder } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  mapTreeData: {
    type: Array,
    default: () => []
  },
  defaultProps: {
    type: Object,
    default: () => ({})
  },
  cadLayerUrl: {
    type: String,
    default: ""
  },
  mergeSvgUrl: {
    type: String,
    default: ""
  },
  expandedKeys: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['node-click'])

// Refs
const treeRef = ref(null)

// Methods
const handleNodeClick = (data) => {
  emit('node-click', data)
}

const getLayerStatus = (type) => {
  if (type === 'cad') {
    return props.cadLayerUrl ? 'success' : 'info'
  } else if (type === 'xmap') {
    return props.mergeSvgUrl ? 'success' : 'info'
  }
  return 'info'
}

const getLayerStatusText = (type) => {
  if (type === 'cad') {
    return props.cadLayerUrl ? '已上传' : '未上传'
  } else if (type === 'xmap') {
    return props.mergeSvgUrl ? '已上传' : '未上传'
  }
  return ''
}

// 刷新方法
const refresh = () => {
  // 可以在这里添加刷新逻辑
  console.log('LeftPanel refresh called')
}

// 暴露方法给父组件
defineExpose({
  refresh
})
</script>

<style lang="scss" scoped>
.left-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.panel-content {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 4px 0;
  
  .node-icon {
    margin-right: 8px;
    color: var(--el-color-primary);
  }
  
  .node-label {
    flex: 1;
    font-size: 14px;
    color: var(--el-text-color-primary);
  }
  
  .node-status {
    margin-left: 8px;
  }
}

:deep(.el-tree) {
  background: transparent;
  
  .el-tree-node {
    .el-tree-node__content {
      padding: 8px;
      border-radius: 4px;
      margin-bottom: 2px;
      
      &:hover {
        background-color: var(--el-color-primary-light-9);
      }
      
      &.is-current {
        background-color: var(--el-color-primary-light-8);
        color: var(--el-color-primary);
      }
    }
    
    .el-tree-node__expand-icon {
      color: var(--el-text-color-secondary);
      
      &.is-leaf {
        color: transparent;
      }
    }
  }
}
</style>
