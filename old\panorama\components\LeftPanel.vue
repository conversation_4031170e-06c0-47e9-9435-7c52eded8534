<template>
  <div class="left-panel">
    <div class="map-tree">
      <el-tree
        ref="mapTree"
        :data="mapTreeData"
        :props="defaultProps"
        @node-click="handleNodeClick"
        node-key="id"
        :default-expanded-keys="expandedKeys"
        :highlight-current="true"
        :expand-on-click-node="false"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <span>{{ node.label }}</span>
        </div>
      </el-tree>
    </div>
  </div>
</template>

<script>
export default {
  name: "LeftPanel",
  props: {
    mapTreeData: {
      type: Array,
      required: true
    },
    defaultProps: {
      type: Object,
      required: true
    },
    cadLayerUrl: {
      type: String,
      default: ""
    },
    xmapLayerUrl: {
      type: String,
      default: ""
    },
    expandedKeys: {
      type: Array,
      default: () => ['1']
    }
  },
  methods: {
    handleNodeClick(data) {
      this.$emit('node-click', data);
    },
    /* 图层显示/隐藏方法，暂时注释掉，未来可能使用
    toggleLayerVisibility(data) {
      this.$emit('toggle-layer-visibility', data);
    }
    */
    // 刷新树形组件
    refresh() {
      console.log('LeftPanel: 刷新树形组件');
      // 强制刷新树形组件
      this.$nextTick(() => {
        // 使用展开/折叠的方式刷新树形组件，而不是使用filter
        if (this.$refs.mapTree) {
          // 获取当前展开的节点
          const expandedKeys = [...this.expandedKeys];

          // 先折叠所有节点
          this.$refs.mapTree.store.setData(this.mapTreeData);

          // 然后重新展开之前展开的节点
          expandedKeys.forEach(key => {
            const node = this.$refs.mapTree.getNode(key);
            if (node) {
              node.expanded = true;
            }
            this.$emit('node-click', node);
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";

/* 组件特定样式 */
.is-hidden {
  opacity: 0.5;
  text-decoration: line-through;
}
</style>
