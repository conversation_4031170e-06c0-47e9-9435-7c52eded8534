<template>
	<div class="app-container">

		<el-form :model="queryParams" :inline="true" label-width="68px">
			<el-form-item label="点位名称">
				<el-input v-model="queryParams.instanceName" placeholder="请输入点位名称" clearable></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handleQuery">搜索</el-button>
				<el-button @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>

		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<router-link to="/point/add">
					<el-button type="primary" plain @click="">添加</el-button>
				</router-link>
				
			</el-col>
			<el-col :span="1.5">
				<el-button type="info" plain @click="handleImport">导入</el-button>
			</el-col>
		</el-row>

		<el-table v-loading="loading" :data="dataList">
			<el-table-column label="点位名称" align="center" prop="instanceName" />
			<el-table-column label="所属部门" align="center" prop="deptName" />
			<el-table-column label="Toolgroup" align="center" prop="toolGroup" />
			<el-table-column label="Toolid" align="center" prop="toolId" />
			<el-table-column label="点位类型" align="center" prop="className" />
			<el-table-column label="编辑状态" align="center">
				<template #default="scope">
					{{ scope.row.editStatus }}
				</template>
			</el-table-column>
			<el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
				<template #default="scope">
					<el-button link type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
					<el-button link type="primary" @click="handleDelete(scope.row)">删除</el-button>
					<el-button link type="primary" @click="">参数</el-button>
				</template>
			</el-table-column>
		</el-table>
		<pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

		<el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
			<el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
				<el-icon class="el-icon--upload"><upload-filled /></el-icon>
				<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
				<template #tip>
					<div class="el-upload__tip text-center">
						<div class="el-upload__tip">
							<el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
						</div>
						<span>仅允许导入xls、xlsx格式文件。</span>
						<el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
					</div>
				</template>
			</el-upload>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" @click="submitFileForm">确 定</el-button>
					<el-button @click="upload.open = false">取 消</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script>
	import { getToken } from "@/utils/auth";
	import point from '@/api/point/point';

	export default {
		data() {
			return {
				dataList: [],
				loading: false,
				total: 0,
				upload: {
					// 是否显示弹出层
					open: false,
					// 弹出层标题
					title: "点位导入",
					// 是否禁用上传
					isUploading: false,
					// 是否更新已经存在的数据
					updateSupport: 0,
					// 设置上传的请求头部
					headers: {
						Authorization: "Bearer " + getToken()
					},
					// 上传的地址
					url: import.meta.env.VITE_APP_BASE_API + "/point/importData"
				},
				form: {},
				queryParams: {
					pageNum: 1,
					pageSize: 10
				},
				rules: {}
			}
		},
		mounted() {
			this.getList()
		},
		methods: {
			getList() {
				this.loading = false
			},
			handleAdd() {
				this.$refs.addDialogRef.init()
			},
			resetQuery() {

			},
			handleQuery() {

			},
			handleUpdate(row) {

			},
			handleDelete(row) {

			},
			importTemplate() {
			  this.download("point/importData", {
			  }, `point_template_${new Date().getTime()}.xlsx`)
			},
			handleImport() {
				this.upload.open = true
			},
			handleFileUploadProgress(event, file, fileList) {
				this.upload.isUploading = true
			},
			handleFileSuccess(response, file, fileList) {
				this.upload.open = false
				this.upload.isUploading = false
				this.$refs["uploadRef"].handleRemove(file)
				this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
					response.msg + "</div>", "导入结果", {
						dangerouslyUseHTMLString: true
					})
				this.getList()
			},
			submitFileForm() {
				this.$refs["uploadRef"].submit()
			}
		}
	}
</script>

<style>

</style>