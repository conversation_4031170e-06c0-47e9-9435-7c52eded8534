import request from '@/utils/request'

// 手动输出命令
export function handMovementOrder(query) {
  return request({
    url: '/deploy/handMovementOrder',
    method: 'post',
    data: query
  })
}

// 服务器后台程序重启
export function restartJar(query) {
  return request({
    url: '/deploy/restartJar',
    method: 'post',
    data: query
  })
}

// 缓存查询
export function getCacheValue(cacheKey) {
  return request({
    url: '/deploy/getCacheValue/'+ cacheKey,
    method: 'get'
  })
}

// 后台jar包历史备份(服务器上)
export function getJarVersionList(data) {
  return request({
    url: '/deploy/getJarVersionList',
    params: data,
    method: 'get'
  })
}

// 前台包历史备份(服务器上)
export function getHtmlVersionList(data) {
  return request({
    url: '/deploy/getHtmlVersionList',
    params: data,
    method: 'get'
  })
}

// jar包回滚
export function jarVersionRollBack(query) {
  return request({
    url: '/deploy/jarVersionRollBack',
    data: query,
    method: 'post'
  })
}

// dist回滚
export function htmlVersionRollBack(query) {
  return request({
    url: '/deploy/htmlVersionRollBack',
    data: query,
    method: 'post'
  })
}

// DC启动
export function DCstart(query) {
  return request({
    url: '/deploy/DCstart',
    data: query,
    method: 'post'
  })
}



