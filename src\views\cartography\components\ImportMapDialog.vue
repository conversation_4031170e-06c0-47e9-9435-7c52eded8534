<template>
  <el-dialog 
    v-model="dialogVisible" 
    title="导入地图图层" 
    width="500px" 
    @close="handleClose"
  >
    <el-upload
      ref="uploadRef"
      class="upload-demo"
      drag
      action="#"
      :http-request="handleImportUpload"
      :limit="1"
      :file-list="fileList"
      :auto-upload="false"
      :on-change="handleFileChange"
      :before-upload="beforeUpload"
    >
      <el-icon class="el-icon--upload">
        <UploadFilled />
      </el-icon>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip">
          <div class="tip-section">
            <h4>支持的文件格式：</h4>
            <ul>
              <li><strong>CAD图层：</strong> .svg 矢量图文件</li>
              <li><strong>XMAP图层：</strong> .xmap 格式文件</li>
            </ul>
          </div>
          <div class="tip-section important">
            <h4>重要提示：</h4>
            <ul>
              <li>必须优先导入CAD图层</li>
              <li>CAD图层不允许重复导入</li>
              <li>文件大小限制：50MB</li>
            </ul>
          </div>
        </div>
      </template>
    </el-upload>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :disabled="fileList.length === 0">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ImportMapDialog">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  cadLayerUrl: {
    type: String,
    default: ""
  }
})

// Emits
const emit = defineEmits(['update:visible', 'file-change', 'submit', 'cancel'])

// Refs
const uploadRef = ref(null)
const fileList = ref([])

// Computed
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// Methods
const handleFileChange = (file) => {
  fileList.value = [file]
  emit('file-change', file)
}

const beforeUpload = (file) => {
  // 检查文件类型
  const fileName = file.name.toLowerCase()
  const isCAD = fileName.endsWith('.svg')
  const isXMAP = fileName.endsWith('.xmap')
  
  if (!isCAD && !isXMAP) {
    ElMessage.error('只支持 .svg 和 .xmap 格式的文件!')
    return false
  }
  
  // 检查文件大小
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过 50MB!')
    return false
  }
  
  return true
}

const handleImportUpload = () => {
  // 这个方法不再直接处理上传，而是由submitImportMap方法处理
  console.log('handleImportUpload被调用，但不再直接处理上传')
  return true // 返回true，阻止组件默认的上传行为
}

const handleSubmit = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  // 获取文件扩展名
  const fileName = fileList.value[0].name
  const fileExt = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()

  // 判断是否为CAD文件（svg格式）
  const isCADFile = fileExt === 'svg'

  // 判断是否为XMAP文件（xmap格式）
  const isXMAPFile = fileExt === 'xmap'

  console.log('ImportMapDialog - 文件类型:', { isCADFile, isXMAPFile })
  console.log('ImportMapDialog - 当前CAD图层URL:', props.cadLayerUrl)

  // 判断1：当CAD图层为空时，要求必须先导入CAD图层
  if (isXMAPFile && !props.cadLayerUrl) {
    ElMessage.warning('必须先导入CAD图层，再导入XMAP图层')
    return
  }

  // 判断2：当已经存在CAD图层时，不允许重复导入
  if (isCADFile && props.cadLayerUrl) {
    ElMessage.warning('CAD图层已存在，不允许重复导入')
    return
  }

  // 将文件类型信息一并传递给父组件
  emit('submit', {
    file: fileList.value[0],
    isCADFile,
    isXMAPFile
  })
}

const handleCancel = () => {
  fileList.value = []
  emit('cancel')
}

const handleClose = () => {
  fileList.value = []
  emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
.upload-demo {
  :deep(.el-upload) {
    width: 100%;
  }
  
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 180px;
    border: 2px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    
    &:hover {
      border-color: var(--el-color-primary);
    }
    
    .el-icon--upload {
      font-size: 67px;
      color: var(--el-text-color-placeholder);
      margin: 40px 0 16px;
      line-height: 50px;
    }
    
    .el-upload__text {
      color: var(--el-text-color-regular);
      font-size: 14px;
      text-align: center;
      
      em {
        color: var(--el-color-primary);
        font-style: normal;
      }
    }
  }
}

.el-upload__tip {
  margin-top: 16px;
  
  .tip-section {
    margin-bottom: 16px;
    
    &.important {
      padding: 12px;
      background-color: var(--el-color-warning-light-9);
      border: 1px solid var(--el-color-warning-light-7);
      border-radius: 4px;
    }
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        font-size: 13px;
        color: var(--el-text-color-regular);
        line-height: 1.4;
        
        strong {
          color: var(--el-text-color-primary);
        }
      }
    }
  }
  
  .important {
    h4, li {
      color: var(--el-color-warning-dark-2);
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 8px;
  }
}
</style>
