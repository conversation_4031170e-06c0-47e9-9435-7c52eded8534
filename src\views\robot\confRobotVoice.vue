<template>
	<div class="app-container">

		<el-form :model="queryParams" :inline="true" label-width="68px">
			<el-form-item label="语音内容">
				<el-input v-model="queryParams.display" placeholder="请输入语音内容" clearable></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handleQuery">搜索</el-button>
				<el-button @click="resetQuery">重置</el-button>
			</el-form-item>
		</el-form>

		<el-row :gutter="10" class="mb8">
			<el-col :span="1.5">
				<el-button type="primary" plain @click="handleAdd">添加</el-button>
			</el-col>
		</el-row>

		<el-table v-loading="loading" :data="dataList">
			<el-table-column label="场景" align="center" prop="scene" />
			<el-table-column label="语音内容" align="center" prop="display" />
			<el-table-column label="上传时间" align="center" prop="createTime">
				<template #default="scope">
					{{ parseTime(scope.row.createTime) }}
				</template>
			</el-table-column>
			<el-table-column label="上传人" align="center" prop="createBy" />
			<el-table-column label="机器人" align="center" prop="robotNames" show-overflow-tooltip :formatter="robotNamesFormatter"/>
			<el-table-column label="状态" align="center">
				<template #default="scope">
					<dict-tag :options="voice_status" :value="scope.row.status" />
				</template>
			</el-table-column>
			<el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
				<template #default="scope">
					<el-button link type="primary" @click="voicePlay(scope.row)">播放</el-button>
					<el-button link type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
					<el-button link type="primary" @click="issue(scope.row)">重新下发</el-button>
				</template>
			</el-table-column>
		</el-table>
		<pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
			v-model:limit="queryParams.pageSize" @pagination="getList" />

		<el-dialog v-model="dialogVisible" :title="title" width="400" :close-on-press-escape="false" :show-close="false"
			:close-on-click-modal="false">
			<el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
				<el-row :gutter="15">
					<el-col :span="24">
						<el-form-item label="场景" prop="instanceName">
							<el-select v-model="form.scene" placeholder="请选择场景">
								<el-option v-for="item in robot_scene" :label="item.label"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="语音内容" prop="display">
							<el-input v-model="form.display" type="textarea" placeholder="请输入语音内容"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="版本" prop="version">
							<el-input v-model="form.version" type="text" placeholder="请输入版本"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item>
							<FileUpload :fileList="fileList" @update:modelValue="fileUploadUp" accept=".mp3"
								:fileSize="20" :fileType="['mp3']" :limit="1"></FileUpload>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="机器人">
							<el-select v-model="form.robotIds" multiple placeholder="请选择机器人">
								<el-option label="全部" :value="0"></el-option>
								<el-option v-for="item in robotList" :label="item.robotName" :value="item.id" />
							</el-select>
						</el-form-item>
					</el-col>

					<el-col :span="24">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="form.remark" type="textarea" placeholder="请输入备注"></el-input>
						</el-form-item>
					</el-col>

				</el-row>
			</el-form>
			<template #footer>
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handelConfirm">确定</el-button>
			</template>
		</el-dialog>
		
		<audio ref="audio"></audio>
	</div>
</template>

<script>
	import {
		getToken
	} from "@/utils/auth";
	import confRobotVoice from '@/api/robot/confRobotVoice';
	import robot from '@/api/robot/robot';

	export default {
		data() {
			return {
				title: '',
				fileList: [],
				robot_scene: getCurrentInstance().proxy.useDict("robot_scene").robot_scene,
				voice_status: getCurrentInstance().proxy.useDict("voice_status").voice_status,
				proxy: getCurrentInstance().proxy,
				dataList: [],
				formRef: null,
				dialogVisible: false,
				loading: false,
				total: 0,
				form: {},
				queryParams: {
					pageNum: 1,
					pageSize: 10
				},
				rules: {},
				robotList: [],
				apiUrl: import.meta.env.VITE_APP_BASE_API
			}
		},
		mounted() {
			this.getList()
			this.getRobotAll()
		},
		methods: {
			getList() {
				this.loading = true
				confRobotVoice.getList(this.queryParams).then(res => {
					this.dataList = res.rows
					this.total = res.total
					this.loading = false
				})
			},
			issue(row) {
				this.$modal.confirm('是否确认重新下发内容是 ' + row.display + ' 的语音').then(function () {				  
				  return confRobotVoice.issue(row);
				}).then(() => {
				  this.getList()
				  this.$modal.msgSuccess('下发成功')
				}).catch(() => {})
				
			},
			voicePlay(row) {
				this.$refs.audio.src = this.apiUrl + row.voiceUrl				
				this.$refs.audio.load()
				this.$refs.audio.play()
			},
			robotNamesFormatter(row) {
				return row.robotNames.join(",");
			},
			getRobotAll() {
				robot.getAll().then(res=>{
					this.robotList = res.data
				})
			},
			handelConfirm() {
				this.$refs.formRef.validate((valid) => {
					if (!valid) return;
					if (!this.form.id) {
						confRobotVoice.add(this.form).then(res => {
							this.$modal.msgSuccess('添加成功')
							this.dialogVisible = false
							this.getList()
						})
					} else {
						confRobotVoice.edit(this.form).then(res => {
							this.$modal.msgSuccess('编辑成功')
							this.dialogVisible = false
							this.getList()
						})
					}

				})
			},
			handleAdd() {
				this.reset()
				this.title = '语音添加'
				this.dialogVisible = true
			},
			resetQuery() {
				this.queryParams = {
					pageNum: 1,
					pageSize: 10
				}
			},
			reset() {
				this.fileList = []
				this.form = {}
				this.proxy.resetForm('formRef')
			},
			handleQuery() {
				this.getList()
			},
			handleUpdate(row) {
				this.reset()
				this.title = '编辑语音'
				confRobotVoice.getOne(row.id).then(res => {
					this.form = res.data
					this.fileList.push({
						url: res.data.voiceUrl,
						name: res.data.voiceUrl.split("/")[6]
					})
					this.dialogVisible = true
				})
			},
			handleDelete(row) {

			},
			fileUploadUp(res) {
				this.form.voiceUrl = res
			}
		}
	}
</script>

<style>

</style>