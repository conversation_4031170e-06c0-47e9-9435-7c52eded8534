<template>
  <div class="app-container">
    <div class="toolbar">
      <el-button-group>
        <el-button size="small" icon="el-icon-document-add" @click="handleCreateMap">新建地图</el-button>
        <el-button size="small" icon="el-icon-folder-opened" @click="handleOpenMap">打开地图</el-button>
        <!-- <el-button size="small" icon="el-icon-add-location" @click="handleMaintenanceConfig" v-if="xmapLayerUrl">检修配置</el-button> -->
        <el-button size="small" icon="el-icon-upload2" @click="handleImportMap">导入地图</el-button>
        <el-button size="small" icon="el-icon-download" @click="handleDownloadMap" :disabled="!currentMap">下载地图</el-button>
        <el-button size="small" icon="el-icon-delete" @click="handleDeleteMap" :disabled="!currentMap">删除地图</el-button>
      </el-button-group>
    </div>

    <div class="main-content">

      <left-panel
        ref="leftPanel"
        :map-tree-data="mapTreeData"
        :default-props="defaultProps"
        :cad-layer-url="cadLayerUrl"
        :xmap-layer-url="xmapLayerUrl"
        :expanded-keys="expandedKeys"
        @node-click="handleNodeClick"
      />

      <!-- 当activeLayer为xmap时，显示CenterPanelXMap组件 -->
      <center-panel-xmap
        v-if="activeLayer === 'xmap'"
        ref="centerPanelXMap"
        :current-map="currentMap"
        :merge-svg-url.sync="mergeSvgUrl"
        :offset.sync="offset"
        :transform-step="transformStep"
        @update-current-map="processMapData"
      />

      <!-- 当activeLayer为cad时，显示CenterPanel组件 -->
      <center-panel
        v-else-if="activeLayer === 'cad'"
        ref="centerPanel"
        :active-layer="activeLayer"
        :current-map="currentMap"
        :cad-layer-url="cadLayerUrl"
        :cad-transform="cadTransform"
      />

      <!-- 当activeLayer为center时，显示CenterPanelMain组件 -->
      <center-panel-main
        v-else
        ref="centerPanelMain"
        :current-map="currentMap"
        :cad-layer-url="cadLayerUrl"
        :cad-transform="cadTransform"
        :show-work-points="showWorkPoints"
        :show-obstacle-points="showObstaclePoints"
      />

      <right-panel
        :current-map="currentMap"
        :cad-width="cadWidth"
        :cad-height="cadHeight"
      />
    </div>

    <!-- 新建地图对话框 -->
    <create-map-dialog
      :visible.sync="createMapDialogVisible"
      :initial-form="newMapForm"
      :rules="newMapRules"
      @submit="submitCreateMap"
      @cancel="createMapDialogVisible = false"
    />

    <!-- 导入地图对话框 -->
    <import-map-dialog
      :visible.sync="importMapDialogVisible"
      :cad-layer-url="cadLayerUrl"
      @file-change="handleImportFileChange"
      @submit="submitImportMap"
      @cancel="importMapDialogVisible = false"
    />

    <!-- 打开地图对话框 -->
    <open-map-dialog
      :visible.sync="openMapDialogVisible"
      @submit="submitOpenMap"
    />

    <!-- 检修配置对话框 -->
    <maintenance-config
      :visible.sync="maintenanceConfigVisible"
      :current-map="currentMap"
      :cad-layer-url="cadLayerUrl"
      @save="handleSaveMaintenanceConfig"
    />
  </div>
</template>

<script>
import { uploadMapLayer, addPanorama, getLastPanorama, getPanorama } from '@/api/robot/panorama'
import LeftPanel from './components/LeftPanel.vue'
import CenterPanel from './components/CenterPanel.vue'
import CenterPanelXMap from './components/CenterPanelXMap.vue'
import CenterPanelMain from './components/CenterPanelMain.vue'
import RightPanel from './components/RightPanel.vue'
import CreateMapDialog from './components/CreateMapDialog.vue'
import ImportMapDialog from './components/ImportMapDialog.vue'
import OpenMapDialog from './components/OpenMapDialog.vue'
import MaintenanceConfig from './components/MaintenanceConfig.vue'

export default {
  name: "PanoramaMap",
  components: {
    LeftPanel,
    CenterPanel,
    'center-panel-xmap': CenterPanelXMap,  // 使用kebab-case注册组件
    'center-panel-main': CenterPanelMain,  // 使用kebab-case注册组件
    RightPanel,
    CreateMapDialog,
    ImportMapDialog,
    OpenMapDialog,
    MaintenanceConfig
  },
  data() {
    return {
      // 当前地图
      currentMap: null,
      // CAD图层URL
      cadLayerUrl: "",
      // XMAP图层URL
      xmapLayerUrl: "",
      // CAD图层宽度
      cadWidth: null,
      // CAD图层高度
      cadHeight: null,
      // 当前显示的图层
      activeLayer: "center", // 可选值: "center", "cad", "xmap"
      // CAD图层变换属性
      cadTransform: {
        rotation: 0,
        scale: 1,
        translateX: 0,
        translateY: 0
      },
      // XMAP图层基于左上角(0,0)的偏移量(pt)，用于保存
      offset: {
        x: 0,
        y: 0
      },
      // 变换步长
      transformStep: {
        rotation: 1,    // 旋转步长（度）
        translate: 1    // 平移步长（像素）
      },
      // 地图树数据
      mapTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 新建地图对话框
      createMapDialogVisible: false,
      newMapForm: {
        name: '',
        scale: '0.05',
        remark: ''
      },
      newMapRules: {
        name: [
          { required: true, message: '请输入地图名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        scale: [
          { required: true, message: '请输入地图缩放比例', trigger: 'blur' },
          { pattern: /^0*\.0*[1-9][0-9]*$/, message: '请输入大于0且小于1的数字', trigger: 'blur' }
        ]
      },
      // 导入地图对话框
      importMapDialogVisible: false,
      importFileList: [],
      // 打开地图对话框
      openMapDialogVisible: false,
      // 检修配置对话框
      maintenanceConfigVisible: false,
      // 添加一个标志，表示是否显示工作点位
      showWorkPoints: false,
      // 添加一个标志，表示是否显示检修点位
      showObstaclePoints: false,
    };
  },
  created() {
    this.fetchLastModifiedMap();
  },
  computed: {
    // 计算所有需要展开的节点ID
    expandedKeys() {
      const keys = [];
      // 遍历地图树数据，收集所有节点的ID
      this.mapTreeData.forEach(node => {
        if (node.id) {
          keys.push(node.id);
        }
        // 如果有子节点，也收集它们的ID
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            if (child.id) {
              keys.push(child.id);
            }
          });
        }
      });
      return keys;
    }
  },
  methods: {
    // 获取最近一次修改的地图
    fetchLastModifiedMap() {
      // 显示加载中提示
      const loading = this.$loading({
        lock: true,
        text: '正在加载地图数据，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      getLastPanorama().then(response => {
        loading.close();

        if (response.code === 200 && response.data) {
          console.log('获取最新修改的地图数据:', response.data);

          // 处理地图数据
          this.processMapData(response.data);

          this.$message.success(`已加载地图: ${response.data.mapName || ''}`);

          // 刷新组件
          this.refreshComponents();
        } else {
          this.resetMapData();
        }
      }).catch(error => {
        loading.close();
        console.error('获取最近修改的地图失败:', error);
        this.resetMapData();
        this.$message.error('获取地图数据失败');
      });
    },

    // 重置地图数据
    resetMapData() {
      this.mapTreeData = [];
      this.currentMap = null;
      this.cadLayerUrl = "";
      this.xmapLayerUrl = "";
      this.cadWidth = null;
      this.cadHeight = null;
      // 重置偏移量
      this.offset = { x: 0, y: 0 };
    },
    // 处理节点点击
    handleNodeClick(data) {
      // 检查节点是否有类型属性
      if (!data.type) {
        // 如果没有type属性，则可能是根地图节点
        // 修改：点击根节点时显示Main图层
        this.activeLayer = "center";
        // 设置显示工作点位
        this.showWorkPoints = true;
        // 设置显示检修点位
        this.showObstaclePoints = true;

        // 重置CAD图层变换属性
        this.resetCadTransform();

        // 延迟一点时间，确保组件已经渲染
        setTimeout(() => {
          // 如果有主面板，通知它更新点位和路径
          if (this.$refs.centerPanelMain) {
            console.log('通知centerPanelMain更新地图元素');
            this.$refs.centerPanelMain.updateMapElements();
          } else {
            console.warn('centerPanelMain引用不存在');
          }
        }, 100);

        // 获取当前地图下的所有图层
        const layers = this.findMapLayers(data);
        const hasValidLayer = layers.some(layer => {
          if (layer.type === 'cad' && this.cadLayerUrl) return true;
          if (layer.type === 'xmap' && this.xmapLayerUrl) return true;
          return false;
        });

        if (hasValidLayer) {
          // 延迟一点时间，确保组件已经渲染
          setTimeout(() => {
            // 如果有主面板，通知它更新点位和路径
            if (this.$refs.centerPanelMain) {
              console.log('通知centerPanelMain更新地图元素');
              this.$refs.centerPanelMain.updateMapElements();
            } else {
              console.warn('centerPanelMain引用不存在');
            }
          }, 100);
          this.$message.success(`已选择: ${data.label}`);
        }
      } else {
        // 根据节点类型处理
        switch (data.type) {
          case 'cad':
            // 点击CAD图层
            // 设置当前活动图层
            this.activeLayer = "cad";
            // 确保CAD图层的scale为1
            if (this.cadTransform.scale !== 1) {
              console.log('修正CAD图层scale:', this.cadTransform.scale, '-> 1');
              this.cadTransform.scale = 1;
            }
            // 只有当CAD图层已上传时，才设置currentMap和activeLayer
            if (this.cadLayerUrl) {
              // 设置不显示工作点位
              this.showWorkPoints = false;
              this.showObstaclePoints = false;
              this.$message.success(`已选择: ${data.label}`);
            } else {
              this.$message.info('请先上传CAD图层');
            }
            break;
          case 'xmap':
            // 点击XMAP图层
            // 只有当XMAP图层已上传时，才设置activeLayer
            if (this.xmapLayerUrl) {
              // 设置当前活动图层为xmap
              this.activeLayer = "xmap";
              // 设置不显示工作点位
              this.showWorkPoints = false;
              this.showObstaclePoints = false;

              // 直接显示mergeSvgUrl的内容
              if (this.mergeSvgUrl) {
                console.log('显示XMAP图层，使用mergeSvgUrl:', this.mergeSvgUrl);
                // 我们不需要在这里调用任何方法，因为：
                // 1. 设置activeLayer = "xmap"会触发组件的条件渲染
                // 2. 组件的mounted钩子会自动调用initEcharts方法
                // 3. mergeSvgUrl的watch也会触发initEcharts方法
                console.log('等待CenterPanelXMap组件自动初始化...');
              }

              this.$message.success(`已选择: ${data.label}`);
            } else {
              this.$message.info('请先上传XMAP图层');
            }
            break;
          default:
            // 其他类型的图层
            this.$message.info(`未知图层类型: ${data.type}`);
            break;
        }
      }
    },

    // 查找地图下的所有图层
    findMapLayers(mapNode) {
      if (!mapNode.children) {
        return [];
      }
      return mapNode.children.filter(node => node.type === 'cad' || node.type === 'xmap');
    },

    // 新建地图
    handleCreateMap() {
      // 打开新建地图对话框
      this.createMapDialogVisible = true;

      // 重置表单
      this.newMapForm = {
        name: '',
        scale: '0.05',
        remark: ''
      };
    },

    // 提交新建地图
    submitCreateMap(form) {
      // 创建请求数据
      const requestData = {
        name: form.name,
        scale: parseFloat(form.scale), // 将字符串转换为数字
        remark: form.remark
      };

      // 显示加载中提示
      const loading = this.$loading({
        lock: true,
        text: '正在创建地图，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 调用API创建地图
      addPanorama(requestData).then(response => {
        loading.close();

        if (response.code === 200) {
          // 获取返回的地图ID
          const mapId = response.data && response.data.mapId;

          if (!mapId) {
            this.$message.error('创建地图成功，但未获取到有效的地图ID');
            this.createMapDialogVisible = false;
            return;
          }

          console.log('创建地图成功，获取到的地图ID:', mapId);

          // 关闭对话框
          this.createMapDialogVisible = false;
          this.$message.success('创建地图成功，正在刷新页面...');

          // 使用新保存的地图ID刷新页面
          this.$nextTick(() => {
            // 直接调用刷新地图数据方法
            this.refreshMapData(mapId);
          });
        } else {
          this.$message.error(response.msg || '创建地图失败');
        }
      }).catch(error => {
        loading.close();
        console.error('创建地图失败:', error);
        this.$message.error('创建地图失败');
      });
    },

    // 打开地图
    handleOpenMap() {
      // 打开地图对话框
      this.openMapDialogVisible = true;
    },

    // 提交打开地图
    submitOpenMap(map) {
      if (!map || !map.mapId) {
        this.$message.warning('请选择有效的地图');
        return;
      }

      // 显示加载中提示
      const loading = this.$loading({
        lock: true,
        text: '正在加载地图，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 调用API获取地图详情
      getPanorama(map.mapId).then(response => {
        loading.close();
        this.openMapDialogVisible = false;

        if (response.code === 200 && response.data) {
          // 处理地图数据
          this.processMapData(response.data);

          this.$message.success(`已加载地图: ${response.data.mapName || ''}`);

          // 刷新组件
          this.refreshComponents();
        } else {
          this.$message.error(response.msg || '加载地图失败');
        }
      }).catch(error => {
        loading.close();
        console.error('加载地图失败:', error);
        this.$message.error('加载地图失败');
      });
    },

    // 导入地图
    handleImportMap() {
      this.importMapDialogVisible = true;
      this.importFileList = [];
    },

    // 导入文件变更处理
    handleImportFileChange(file) {
      this.importFileList = [file];
    },

    // 提交导入地图
    submitImportMap(fileData) {
      // 兼容旧的调用方式
      const file = fileData.file || fileData;
      const isXMAPFile = fileData.isXMAPFile || false;
      const isCADFile = fileData.isCADFile || false;

      if (!file || !file.raw) {
        this.$message.error('文件对象不可用');
        return;
      }

      // 判断当前是否有可用地图
      if (!this.currentMap) {
        this.$message.error('请先创建或打开地图');
        return;
      }

      // 如果是XMAP文件，检查CAD图层URL是否存在
      if (isXMAPFile && !this.cadLayerUrl) {
        this.$message.error('CAD图层URL为空，请先导入CAD图层');
        return;
      }

      // 如果是CAD文件，检查是否已经存在CAD图层
      if (isCADFile && this.cadLayerUrl) {
        this.$message.warning('CAD图层已存在，不允许重复导入');
        return;
      }

      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file.raw);
      formData.append('mapId', this.currentMap.id);

      // 显示加载中提示
      const loading = this.$loading({
        lock: true,
        text: '正在导入地图，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 调用API上传文件
      uploadMapLayer(formData).then(response => {
        loading.close();

        if (response.code === 200) {
          // 获取新的返回结构中的属性
          const data = response.data || {};

          // 解析返回的数据
          const isXMap = data.isXMap;

          // 如果是XMAP文件，完整刷新地图数据
          if (isXMap) {
            this.$message.success('XMAP图层上传成功，正在刷新地图数据...');

            // 关闭对话框
            this.importMapDialogVisible = false;

            // 使用当前地图ID重新加载完整地图数据
            this.refreshMapData(this.currentMap.id);
          } else {
            // CAD图层处理保持原样
            if (data.cadUrl) {
              // 保存当前的XMAP图层URL
              const savedXmapLayerUrl = this.xmapLayerUrl;

              // 设置CAD图层URL
              this.cadLayerUrl = data.cadUrl;

              // 更新CAD宽度和高度
              if (data.cadWidth) this.cadWidth = data.cadWidth;
              if (data.cadHeight) this.cadHeight = data.cadHeight;

              // 更新CAD图层节点
              const cadNode = this.mapTreeData[0].children.find(item => item.id === `${this.currentMap.id}-1`);
              if (cadNode) {
                cadNode.visible = true;
              }

              // 重置CAD图层变换属性，确保左上角对齐
              this.cadTransform = {
                rotation: 0,
                scale: 1,
                translateX: 0,
                translateY: 0
              };

              // 恢复XMAP图层URL，确保不会丢失
              if (savedXmapLayerUrl) {
                this.xmapLayerUrl = savedXmapLayerUrl;
              }

              this.$message.success('CAD图层导入成功');

              // 关闭对话框
              this.importMapDialogVisible = false;
            } else {
              this.$message.error('导入的CAD图层URL为空');
              return;
            }
          }
        } else {
          this.$message.error(response.msg || '导入地图失败');
        }
      }).catch(error => {
        loading.close();
        console.error('导入地图失败:', error);
        this.$message.error('导入地图失败');
      });
    },

    // 下载地图
    handleDownloadMap() {
      if (!this.currentMap) {
        this.$message.warning('请先选择地图');
        return;
      }

      this.$message.success(`地图 ${this.currentMap.name} 下载成功`);
    },

    // 删除地图
    handleDeleteMap() {
      if (!this.currentMap) {
        this.$message.warning('请先选择地图');
        return;
      }

      this.$confirm('确认删除该地图吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从树中删除节点
        const children = this.mapTreeData[0].children;
        const index = children.findIndex(item => item.id === this.currentMap.id);
        if (index !== -1) {
          children.splice(index, 1);

          // 重置地图数据
          this.resetMapData();

          this.$message.success('删除地图成功');
        }
      }).catch(() => {});
    },

    // 刷新地图数据
    refreshMapData(mapId) {
      if (!mapId) {
        this.$message.error('地图ID无效，无法刷新');
        return;
      }

      // 显示加载中提示
      const loading = this.$loading({
        lock: true,
        text: '正在刷新地图数据，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 调用API获取地图详情
      getPanorama(mapId).then(response => {
        loading.close();

        if (response.code === 200 && response.data) {
          // 获取地图数据
          const mapData = response.data;

          // 处理地图数据
          this.processMapData(mapData);

          // 显示成功消息
          this.$message.success(`地图数据刷新成功: ${mapData.mapName || ''}`);

          // 强制刷新组件
          this.activeLayer = 'center';
          this.refreshComponents();
        } else {
          this.$message.error(response.msg || '刷新地图数据失败');
        }
      }).catch(error => {
        loading.close();
        console.error('刷新地图数据失败:', error);
        this.$message.error('刷新地图数据失败');
      });
    },

    // 处理地图数据
    processMapData(mapData) {
      console.log('获取地图信息', mapData);
      // 创建地图节点
      const mapNode = {
        id: mapData.mapId,
        label: mapData.mapName || '',
        scale: mapData.scale || 0.05,
        remark: mapData.remark || '',
        // XMAP相关属性
        xmapWidth: mapData.xmapWidth,
        xmapHeight: mapData.xmapHeight,
        xmapVersion: mapData.xmapVersion,
        minPosX: mapData.minPosX,
        minPosY: mapData.minPosY,
        maxPosX: mapData.maxPosX,
        maxPosY: mapData.maxPosY,
        // XMAP图层的pt单位尺寸
        xmapSvgWidth: mapData.xmapSvgWidth,
        xmapSvgHeight: mapData.xmapSvgHeight,
        // XMAP图层URL
        mergeSvgUrl: mapData.mergeSvgUrl,
        // 偏移量
        offset: { x: mapData.offsetX || 0, y: mapData.offsetY || 0 },
        children: [{
          id: `${mapData.mapId}-1`,
          label: 'CAD图层',
          visible: true,
          type: 'cad'
        }, {
          id: `${mapData.mapId}-2`,
          label: 'XMAP图层',
          visible: true,
          type: 'xmap'
        }]
      };

      // 清空现有树结构并设置新的根节点
      this.mapTreeData = [mapNode];

      // 设置当前地图
      this.currentMap = {
        id: mapData.mapId,
        name: mapData.mapName || '',
        scale: mapData.scale || 0.05,
        remark: mapData.remark || '',
        // XMAP相关属性
        xmapWidth: mapData.xmapWidth,
        xmapHeight: mapData.xmapHeight,
        xmapVersion: mapData.xmapVersion,
        minPosX: mapData.minPosX,
        minPosY: mapData.minPosY,
        maxPosX: mapData.maxPosX,
        maxPosY: mapData.maxPosY,
        // XMAP图层的pt单位尺寸
        xmapSvgWidth: mapData.xmapSvgWidth,
        xmapSvgHeight: mapData.xmapSvgHeight,
        // XMAP图层URL
        mergeSvgUrl: mapData.mergeSvgUrl,
        // 偏移量
        offset: { x: mapData.offsetX || 0, y: mapData.offsetY || 0 },
        // 点位和路径信息
        advancedPoints: mapData.advancedPoints || [],
        advancedRoutes: mapData.advancedRoutes || [],
        // 障碍点
        obstacles: mapData.obstacles || [],
      };

      // 设置图层URL和尺寸
      this.cadLayerUrl = mapData.cadUrl || "";
      this.cadWidth = mapData.cadWidth || null;
      this.cadHeight = mapData.cadHeight || null;
      this.xmapLayerUrl = mapData.xmapSvgUrl || "";
      this.mergeSvgUrl = mapData.mergeSvgUrl || "";

      // 设置偏移量（pt单位）
      this.offset = { x: mapData.offsetX || 0, y: mapData.offsetY || 0 };

      // 记录点位和路径信息
      if (mapData.advancedPoints && mapData.advancedPoints.length > 0) {
        console.log('地图包含点位信息:', mapData.advancedPoints.length, '个点位');
      }

      if (mapData.advancedRoutes && mapData.advancedRoutes.length > 0) {
        console.log('地图包含路径信息:', mapData.advancedRoutes.length, '条路径');
      }

      if (mapData.obstacles && mapData.obstacles.length > 0) {
        console.log('地图包含障碍点信息:', mapData.obstacles.length, '个障碍点');
      }
    },

    // 刷新组件
    refreshComponents() {
      this.$nextTick(() => {
        // 刷新左侧面板
        if (this.$refs.leftPanel) {
          this.$refs.leftPanel.refresh();
        }

        // 根据当前活动图层，更新相应的面板组件
        if (this.activeLayer === 'xmap') {
          // XMAP图层组件会在挂载后自动初始化
          console.log('等待CenterPanelXMap组件自动初始化');
        } else if (this.activeLayer === 'cad') {
          // 延迟一点时间，确保组件已经渲染
          setTimeout(() => {
            // 更新CAD面板
            if (this.$refs.centerPanel) {
              this.$refs.centerPanel.updateMapElements();
            } else {
              console.warn('刷新组件：centerPanel引用不存在');
            }
          }, 100);
        } else if (this.$refs.centerPanelMain) {
          // 延迟一点时间，确保组件已经渲染
          setTimeout(() => {
            // 更新主面板
            if (this.$refs.centerPanelMain) {
              this.$refs.centerPanelMain.updateMapElements();
            } else {
              console.warn('刷新组件：centerPanelMain引用不存在');
            }
          }, 100);
        }
      });
    },

    // 重置CAD图层变换
    resetCadTransform() {
      this.cadTransform = {
        rotation: 0,
        scale: 1,
        translateX: 0,
        translateY: 0
      };
    },

    // 处理检修配置
    handleMaintenanceConfig() {
      if (!this.currentMap) {
        this.$message.warning('请先选择或创建地图');
        return;
      }

      if (!this.cadLayerUrl) {
        this.$message.warning('请先上传CAD图层');
        return;
      }

      if (!this.xmapLayerUrl) {
        this.$message.warning('请先上传XMAP图层');
        return;
      }

      // 打开检修配置对话框
      this.maintenanceConfigVisible = true;
    },

    // 处理保存检修配置
    handleSaveMaintenanceConfig(maintenancePoints) {
      if (!this.currentMap) {
        this.$message.error('当前没有选择地图');
        return;
      }

      // 这里可以调用API保存检修点配置
      console.log('保存检修点配置:', maintenancePoints);

      // 更新当前地图的故障检修点
      this.currentMap.obstacles = maintenancePoints;

      // 刷新地图显示
      this.refreshComponents();

      this.$message.success('检修配置保存成功');
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/panorama.scss";
</style>
