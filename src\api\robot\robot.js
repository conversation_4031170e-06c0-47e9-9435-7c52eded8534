import request from '@/utils/request'

const add = (params) =>{
	return request.post('robot/add', params)
}

const remove = (ids) =>{
	return request.get('robot/remove/' + ids)
}

const edit = (params) =>{
	return request.post('robot/edit', params)
}

const getList = (params) =>{
	return request.get('robot/getList', {params: params})
}

const getAll = (params) =>{
	return request.get('robot/getAll', {params: params})
}

const getOne = (id) =>{
	return request.get('robot/getOne/' + id)
}

const restart = (params) =>{
	return request.post('robot/restart', params)
}

export default {
	add, remove, edit, getList, getAll, getOne, restart
}