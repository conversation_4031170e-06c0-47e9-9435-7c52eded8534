import request from '@/utils/request'

// 查询列表
export function list(query) {
    return request({
        url: '/iden/ruleExpr/list',
        method: 'get',
        params: query
    })
}

// 查询列表
export function getIdenParam(query) {
    return request({
        url: '/iden/ruleExpr/getIdenParam',
        method: 'get',
        params: query
    })
}

// 添加模型-规则
export function addIdenRuleExpr(data) {
    return request({
        url: '/iden/ruleExpr/addIdenRuleExpr',
        method: 'post',
        data: data
    })
}

// 编辑模型-规则
export function updateIdenRuleExpr(data) {
    return request({
        url: '/iden/ruleExpr/updateIdenRuleExpr',
        method: 'post',
        data: data
    })
}

// 删除模型-规则
export function removeIdenRuleExpr(data) {
    return request({
        url: '/iden/ruleExpr/removeIdenRuleExpr',
        method: 'post',
        data: data
    })
}