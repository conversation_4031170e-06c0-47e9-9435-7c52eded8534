<template>
	<div class="page">
		<el-row :gutter="20">
			<el-col :span="6">&nbsp;</el-col>
			<el-col :span="6">
				<h5>前端版本：<a @click="htmlVersionClick"><span style="color: #00aa00;">{{htmlVersion}}</span></a></h5>
			</el-col>
			<el-col :span="6">
				<h5>后台版本：<a @click="jarVersionClick"><span style="color: #00aa00;">{{jarVersion}}</span></a></h5>
			</el-col>
			<el-col :span="6">&nbsp;</el-col>

			<el-col :span="12">
				<el-form ref="form" :model="form" :rules="rules" label-width="100" label-position="top">
					<el-form-item label="服务器IP">
						<el-input v-model="form.host"></el-input>
					</el-form-item>
					<el-form-item label="服务器用户名">
						<el-input v-model="form.userName"></el-input>
					</el-form-item>
					<el-form-item label="服务器密码">
						<el-input show-password v-model="form.password"></el-input>
					</el-form-item>
					<el-form-item label="更新类型">
						<el-select v-model="form.editType" style="width: 200px;">
							<el-option label="后台程序" :value="1"></el-option>
							<el-option label="前端程序" :value="2"></el-option>
						</el-select>&nbsp;
						<el-input v-if="form.editType == 1" v-model="form.path" readonly style="width: 50%;"></el-input>
						<el-select v-if="form.editType == 2" v-model="form.path"  style="width: 400px;">
							<el-option v-for="item in htmlPath" :label="item" :value="item"></el-option>
						</el-select>
						<el-tooltip style="margin-left: 10px;" v-if="form.editType == 2" class="item" effect="dark"
							content="Docker单服务器版选择第二项" placement="right">
							<i class="el-icon-question"></i>
						</el-tooltip>
					</el-form-item>
					<el-form-item label="更新文件">
						<div style="display: block;">
							<el-upload ref="upload" :limit="1" accept=".zip" :headers="upload.headers" :action="upload.url"
								:disabled="upload.isUploading" :on-progress="handleFileUploadProgress"
								:on-success="handleFileSuccess" :auto-upload="false" drag>
								<el-icon class="el-icon--upload"><upload-filled /></el-icon>
								<div class="el-upload__text">仅允许导入zip格式文件。将文件拖到此处，或<em>点击上传</em></div>
								<template #tip>
									<div class="el-upload__tip text-center">									
										<span></span>									
									</div>
								</template>
							</el-upload>
							<div style="margin-top: 10px;">
								<el-popconfirm title="请确认更新类型与文件是否对应" @confirm="submitFileForm" icon-color="red"
									icon="el-icon-warning">
									<template #reference>
										<el-button type="primary">上传到服务器</el-button>
									</template>
								</el-popconfirm>
							</div>
						</div>
						
					</el-form-item>

					<el-form-item v-if="handSwitch">
						<el-input v-model="form.order" clearable @keyup.enter.native="submithandForm"></el-input>
						<el-button style="margin-top: 5px;" @click="submithandForm">执行</el-button>
					</el-form-item>
				</el-form>
			</el-col>
			<el-col :span="12">
				<el-form label-position="top">
					<el-form-item label="控制台">
						<el-input id="scroll_text" type="textarea" v-model="msg" readonly :rows="26"></el-input>						
						<el-button @click="terminal" style="margin-top: 10px;">终端</el-button>
					</el-form-item>
				</el-form>
			</el-col>
		</el-row>

		<el-dialog title="前端历史版本" v-model="htmlVersionVisible">
			<el-select v-model="htmlVersionVisiblePath" @change="htmlVersionClick()" style="margin-bottom: 10px;">
				<el-option v-for="item in htmlPath" :label="item" :value="item"></el-option>
			</el-select>
			<el-tooltip style="margin-left: 10px;" class="item" effect="dark" content="Docker单服务器版选择第二项"
				placement="right">
				<i class="el-icon-question"></i>
			</el-tooltip>
			<el-table :data="htmlVersionData">
				<el-table-column align="center" label="日期" prop="htmlBackupDate"></el-table-column>
				<el-table-column align="center" label="操作">
					<template slot-scope="scope">
						<el-button type="danger" @click="htmlVersionRollBackClick(scope.row)">回滚</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-dialog>

		<el-dialog title="后台历史版本" v-model="jarVersionVisible">
			<el-table :data="jarVersionData">
				<el-table-column align="center" label="版本" prop="jarName"></el-table-column>
				<el-table-column align="center" label="日期" prop="jarBackupDate"></el-table-column>
				<el-table-column align="center" label="操作">
					<template slot-scope="scope">
						<el-button type="danger" @click="jarVersionRollBackClick(scope.row)">回滚</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-dialog>

		<el-dialog title="终端控制台" v-model="dialogVisible" width="1250px" :close-on-click-modal="false"
			destroy-on-close>
			<webssh-dialog ref="websshDialog"></webssh-dialog>
		</el-dialog>
	</div>
</template>

<script>
	import {
		getToken
	} from "@/utils/auth";
	import {
		getServer,
		getServerIp
	} from "@/api/monitor/server";
	import {
		handMovementOrder,
		restartJar,
		getCacheValue,
		getJarVersionList,
		jarVersionRollBack,
		getHtmlVersionList,
		htmlVersionRollBack,
		DCstart
	} from "@/api/deploy/deploy"
	import websshDialog from "./webssh"
	import packageJson from '../../../package.json';

	export default {
		components: {
			websshDialog
		},
		data() {
			return {
				htmlVersionVisiblePath: '/home/<USER>/',
				serverIp: '',
				rules: {
					dcHost: [{
						required: true,
						message: "数据采集服务器IP不能为空",
						trigger: "blur"
					}, {
						pattern: /^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}$/,
						message: "输入数据采集服务器IP格式错误",
						trigger: "blur"
					}],
				},
				dialogVisible: false,
				htmlVersionData: [],
				htmlVersionVisible: false,
				jarVersionData: [],
				jarVersionVisible: false,
				htmlVersion: '',
				jarVersion: '',
				handSwitch: false,
				order: '',
				msg: '',
				form: {},
				upload: {
					// 是否禁用上传
					isUploading: false,
					// 设置上传的请求头部
					headers: {
						Authorization: "Bearer " + getToken()
					},
					// 上传的地址
					url: import.meta.env.VITE_APP_BASE_API + "/deploy/importFile"
				},
				htmlPath: ['/home/<USER>/', '/home/<USER>/html/']
			}
		},
		watch: {
			'form.editType'(row) {
				if (row == 1) this.form.path = "/home/<USER>/robot-admin/";
				if (row == 2) this.form.path = "/home/<USER>/";
			},
			'form.host'(row) {
				if(row != null) {
					sessionStorage.setItem('serverIp', row)					
				}				
			}
		},
		created() {
			this.getServerIp()
			this.version()
		},
		methods: {
			htmlVersionRollBackClick(row) {
				this.$confirm('此操作会将版本回退到之前,请谨慎,是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					let query = JSON.parse(JSON.stringify(this.form))
					query.path = this.htmlVersionVisiblePath
					query.rollbackHistory = row.htmlBackupDate
					htmlVersionRollBack(query).then(res => {
						this.htmlVersionVisible = false
						this.msg += res
						this.textareaScrollTop()
					})
				}).catch(() => {

				});
			},
			htmlVersionClick() {
				let query = JSON.parse(JSON.stringify(this.form))
				query.path = this.htmlVersionVisiblePath
				getHtmlVersionList(query).then(res => {
					this.htmlVersionData = res
					this.htmlVersionVisible = true
				})
			},
			jarVersionRollBackClick(row) {
				this.$confirm('此操作会将版本回退到之前,请谨慎,是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					if (this.form.dcHost == null || this.form.dcHost == '') {
						return this.$message({
							message: '请填写数据采集服务器IP后在操作',
							type: 'warning'
						});
					}
					this.form.rollbackHistory = row.jarBackupDate + "/" + row.jarName
					jarVersionRollBack(this.form);
					this.jarVersionVisible = false
					this.msg += '\n------------------- 服务器后台程序，即将重新启动，请不要刷新页面 -------------------\n\n'
					this.jarTimeout()
					this.textareaScrollTop()
				}).catch(() => {

				});
			},
			jarVersionClick() {
				getJarVersionList(this.form).then(res => {
					this.jarVersionData = res
					this.jarVersionVisible = true
				})
			},
			// 版本号获取
			version() {
				let htmlversion = packageJson.version
				if (htmlversion == undefined) {
					this.htmlVersion = '未知'
				} else {
					this.htmlVersion = htmlversion
				}

				getCacheValue("jarVersion").then(response => {
					if (response == '') {
						this.jarVersion = '未知'
					} else {
						this.jarVersion = response
					}

				});
			},
			terminal() {
				this.dialogVisible = true
				setTimeout(() => {
					this.$refs.websshDialog.init(this.form);
				}, 100)

			},
			reset() {
				this.form = {
					host: '',
					userName: 'root',
					password: 'Qq123',
					path: '/home/<USER>/robot-admin/',
					editType: 2,
				}
			},
			getServerIp() {
				this.reset()
				getServerIp().then(response => {
					this.form.host = response.msg
				});
			},
			submithandForm() {
				handMovementOrder(this.form).then(res => {
					this.msg += res
					this.form.order = ""
					this.textareaScrollTop()
				})
			},
			submitFileForm() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.$refs.upload.submit();
					}
				})
			},
			// 文件上传中处理
			handleFileUploadProgress(event, file, fileList) {
				this.upload.isUploading = true;
			},
			// 文件上传成功处理
			handleFileSuccess(response, file, fileList) {
				this.upload.isUploading = false;
				this.$refs.upload.clearFiles();
				this.msg += response.split("#")[0]
				if (this.form.editType == 1) {
					this.form.historyjarrep = response.split("#")[1]
					restartJar(this.form);
					this.textareaScrollTop()

					setTimeout(() => {
						getServer().then(data => {}, data => {
							this.jarTimeout()
						})
					}, 1000 * 5)

				} else {
					this.textareaScrollTop()
				}

			},
			// 控制台置底
			textareaScrollTop() {
				this.$nextTick(() => {
					const textarea = document.getElementById('scroll_text');
					textarea.scrollTop = textarea.scrollHeight;
				})
			},
			jarTimeout() {
				setTimeout(() => {
					getServer().then(data => {
						console.log("连接成功")
						this.msg += this.parseTime(new Date) + "  后台程序部署完成~~~\n\n"
						DCstart(this.form).then(res => {
							this.msg += res
							this.textareaScrollTop()
						})
						this.textareaScrollTop()
					}, data => {
						console.log("连接失败")
						this.jarTimeout()
					})
				}, 1000 * 10)
			}
		},
	}
</script>

<style scoped>
	.page {
		padding: 30px 50px 0 50px;
	}
</style>