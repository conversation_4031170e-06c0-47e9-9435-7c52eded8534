<template>
	<div>
		<video id="video" controls></video>
	</div>
</template>

<script>
	// import flvjs from "flv.js"

	export default {
		data() {
			return {
				url: "ws://127.0.0.1:8099/websocket/camera",
				message: "",
				text_content: "",
				ws: null,
				flvPlayer: null
			};
		},
		methods: {
			flvPlayer() {
				if (flvjs.isSupported()) {
					let video = document.getElementById('video')
					this.flvPlayer = flvjs.createPlayer({
						type: "flv",
						//是否是实时流
						isLive: true,
						//是否有音频
						hasAudio: false,
						url: this.url
					}, {
						enableStashBuffer: false,
						stashInitialSize: 1024, // 初始缓存大小
					});
					this.flvPlayer.attachMediaElement(video);
					this.flvPlayer.load();
					this.flvPlayer.play();
					this.flvPlayer.on("error", (err) => {
						this.stopSocket()
					});

				}
			},
			stopSocket(row) {
				console.log("连接取消中···")
				if (this.flvPlayer != null) {
					this.flvPlayer.pause();
					this.flvPlayer.unload();
					this.flvPlayer.detachMediaElement();
					this.flvPlayer.destroy();
					this.flvPlayer = null
				}
			},
		},
	};
</script>