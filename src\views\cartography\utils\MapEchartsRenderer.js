/**
 * 地图ECharts渲染工具类
 * 从原始panorama项目迁移并适配Vue3
 */

/**
 * 计算贝塞尔曲线上的点
 * @param {number} t - 参数t，范围[0,1]
 * @param {Array} points - 控制点数组 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
 * @returns {Array} [x, y] 坐标
 */
export function calculateBezierPoint(t, points) {
  if (points.length !== 4) {
    throw new Error('贝塞尔曲线需要4个控制点')
  }
  
  const [p0, p1, p2, p3] = points
  const u = 1 - t
  const tt = t * t
  const uu = u * u
  const uuu = uu * u
  const ttt = tt * t
  
  const x = uuu * p0[0] + 3 * uu * t * p1[0] + 3 * u * tt * p2[0] + ttt * p3[0]
  const y = uuu * p0[1] + 3 * uu * t * p1[1] + 3 * u * tt * p2[1] + ttt * p3[1]
  
  return [x, y]
}

/**
 * 计算贝塞尔曲线上的多个点
 * @param {Array} points - 控制点数组
 * @param {number} segments - 分段数量，默认20
 * @returns {Array} 曲线上的点数组
 */
export function calculateBezierPoints(points, segments = 20) {
  const result = []
  
  for (let i = 0; i <= segments; i++) {
    const t = i / segments
    result.push(calculateBezierPoint(t, points))
  }
  
  return result
}

/**
 * 计算圆弧上的点
 * @param {number} centerX - 圆心X坐标
 * @param {number} centerY - 圆心Y坐标
 * @param {number} radius - 半径
 * @param {number} startAngle - 起始角度（弧度）
 * @param {number} endAngle - 结束角度（弧度）
 * @param {number} segments - 分段数量，默认20
 * @returns {Array} 圆弧上的点数组
 */
export function calculateArcPoints(centerX, centerY, radius, startAngle, endAngle, segments = 20) {
  const result = []
  const angleStep = (endAngle - startAngle) / segments
  
  for (let i = 0; i <= segments; i++) {
    const angle = startAngle + i * angleStep
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)
    result.push([x, y])
  }
  
  return result
}

/**
 * 将角度转换为弧度
 * @param {number} degrees - 角度
 * @returns {number} 弧度
 */
export function degreesToRadians(degrees) {
  return degrees * Math.PI / 180
}

/**
 * 将弧度转换为角度
 * @param {number} radians - 弧度
 * @returns {number} 角度
 */
export function radiansToDegrees(radians) {
  return radians * 180 / Math.PI
}

/**
 * 计算两点之间的距离
 * @param {Array} point1 - 点1 [x, y]
 * @param {Array} point2 - 点2 [x, y]
 * @returns {number} 距离
 */
export function calculateDistance(point1, point2) {
  const dx = point2[0] - point1[0]
  const dy = point2[1] - point1[1]
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * 计算点到直线的距离
 * @param {Array} point - 点 [x, y]
 * @param {Array} lineStart - 直线起点 [x, y]
 * @param {Array} lineEnd - 直线终点 [x, y]
 * @returns {number} 距离
 */
export function calculatePointToLineDistance(point, lineStart, lineEnd) {
  const [x0, y0] = point
  const [x1, y1] = lineStart
  const [x2, y2] = lineEnd
  
  const A = x0 - x1
  const B = y0 - y1
  const C = x2 - x1
  const D = y2 - y1
  
  const dot = A * C + B * D
  const lenSq = C * C + D * D
  
  if (lenSq === 0) {
    // 线段退化为点
    return calculateDistance(point, lineStart)
  }
  
  let param = dot / lenSq
  
  let xx, yy
  
  if (param < 0) {
    xx = x1
    yy = y1
  } else if (param > 1) {
    xx = x2
    yy = y2
  } else {
    xx = x1 + param * C
    yy = y1 + param * D
  }
  
  const dx = x0 - xx
  const dy = y0 - yy
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * 创建ECharts点位系列配置
 * @param {Array} points - 点位数组
 * @param {Object} options - 配置选项
 * @returns {Object} ECharts系列配置
 */
export function createPointSeries(points, options = {}) {
  const {
    name = '点位',
    color = '#409EFF',
    size = 8,
    symbol = 'circle',
    showLabel = true,
    labelFormatter = null
  } = options
  
  const data = points.map(point => ({
    value: [point.x, point.y],
    name: point.name || point.id || '',
    itemStyle: {
      color: point.color || color
    },
    label: {
      show: showLabel,
      formatter: labelFormatter || function(params) {
        return params.data.name || params.dataIndex
      }
    }
  }))
  
  return {
    name,
    type: 'scatter',
    data,
    symbolSize: size,
    symbol,
    itemStyle: {
      color
    },
    emphasis: {
      scale: 1.5,
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 2
      }
    }
  }
}

/**
 * 创建ECharts路径系列配置
 * @param {Array} routes - 路径数组
 * @param {Object} options - 配置选项
 * @returns {Object} ECharts系列配置
 */
export function createRouteSeries(routes, options = {}) {
  const {
    name = '路径',
    color = '#67C23A',
    width = 2,
    type = 'line' // 'line' | 'bezier' | 'arc'
  } = options
  
  const data = routes.map(route => {
    let coords = []
    
    if (type === 'bezier' && route.controlPoints && route.controlPoints.length === 4) {
      coords = calculateBezierPoints(route.controlPoints)
    } else if (type === 'arc' && route.center && route.radius && route.startAngle !== undefined && route.endAngle !== undefined) {
      coords = calculateArcPoints(
        route.center[0], 
        route.center[1], 
        route.radius, 
        degreesToRadians(route.startAngle), 
        degreesToRadians(route.endAngle)
      )
    } else {
      // 默认直线
      coords = route.points || [[route.startX, route.startY], [route.endX, route.endY]]
    }
    
    return {
      coords,
      lineStyle: {
        color: route.color || color,
        width: route.width || width
      }
    }
  })
  
  return {
    name,
    type: 'lines',
    data,
    lineStyle: {
      color,
      width
    },
    emphasis: {
      lineStyle: {
        width: width + 2
      }
    }
  }
}

/**
 * 坐标转换工具类
 */
export class CoordinateTransformer {
  constructor(options = {}) {
    this.scale = options.scale || 1
    this.offsetX = options.offsetX || 0
    this.offsetY = options.offsetY || 0
    this.rotation = options.rotation || 0
  }
  
  /**
   * 将地图坐标转换为屏幕坐标
   * @param {number} x - 地图X坐标
   * @param {number} y - 地图Y坐标
   * @returns {Array} [screenX, screenY]
   */
  mapToScreen(x, y) {
    // 应用缩放
    let screenX = x * this.scale
    let screenY = y * this.scale
    
    // 应用旋转
    if (this.rotation !== 0) {
      const cos = Math.cos(degreesToRadians(this.rotation))
      const sin = Math.sin(degreesToRadians(this.rotation))
      const rotatedX = screenX * cos - screenY * sin
      const rotatedY = screenX * sin + screenY * cos
      screenX = rotatedX
      screenY = rotatedY
    }
    
    // 应用偏移
    screenX += this.offsetX
    screenY += this.offsetY
    
    return [screenX, screenY]
  }
  
  /**
   * 将屏幕坐标转换为地图坐标
   * @param {number} screenX - 屏幕X坐标
   * @param {number} screenY - 屏幕Y坐标
   * @returns {Array} [mapX, mapY]
   */
  screenToMap(screenX, screenY) {
    // 移除偏移
    let x = screenX - this.offsetX
    let y = screenY - this.offsetY
    
    // 移除旋转
    if (this.rotation !== 0) {
      const cos = Math.cos(degreesToRadians(-this.rotation))
      const sin = Math.sin(degreesToRadians(-this.rotation))
      const rotatedX = x * cos - y * sin
      const rotatedY = x * sin + y * cos
      x = rotatedX
      y = rotatedY
    }
    
    // 移除缩放
    x /= this.scale
    y /= this.scale
    
    return [x, y]
  }
}
