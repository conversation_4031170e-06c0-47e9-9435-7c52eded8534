<template>
  <div class="maintenance-map-panel">
    <div class="map-container">
      <!-- 没有选择地图或没有上传任何图层时显示空状态 -->
      <div class="placeholder-map" v-if="!currentMap || !cadLayerUrl">
        <el-empty description="请选择或新建地图"></el-empty>
      </div>
      <!-- 有地图且上传了CAD图层时显示内容 -->
      <div class="map-content" v-else>
        <!-- 使用ECharts显示地图图层 -->
        <div ref="echartsContainer" class="echarts-container"></div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="map-toolbar">
      <el-tooltip content="添加检修点" placement="right">
        <el-button
          size="mini"
          icon="el-icon-plus"
          :type="addingPoint ? 'primary' : ''"
          @click="toggleAddingPoint"
          circle
        ></el-button>
      </el-tooltip>
      <el-tooltip content="缩放重置" placement="right">
        <el-button
          size="mini"
          icon="el-icon-refresh"
          @click="resetView"
          circle
        ></el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import { calculateBezierPoints, calculateArcPoints } from '../utils/MapEchartsRenderer';

export default {
  name: "MaintenanceMapPanel",
  props: {
    currentMap: {
      type: Object,
      default: null
    },
    cadLayerUrl: {
      type: String,
      default: ""
    },
    cadTransform: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      echartsInstance: null,
      // 像素单位的尺寸变量
      cadWidthPx: 0,
      cadHeightPx: 0,
      // pt单位的尺寸变量
      cadWidth: 0,
      cadHeight: 0,
      // pt到px的转换比例
      pt2px: 96 / 72,
      // 基础缩放和位置
      baseScale: 1,
      baseX: 0,
      baseY: 0,
      // 用户交互状态
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0,
      // 视图状态 - 用于记忆缩放和平移
      viewState: {
        scale: 1,
        translateX: 0,
        translateY: 0
      },
      // 缩放配置
      zoomConfig: {
        min: 0.2,  // 最小缩放比例
        max: 20,   // 最大缩放比例
        step: 0.1  // 每次缩放步长
      },
      // 是否正在添加点位
      addingPoint: false,
      // 高亮显示的点位ID
      highlightedPointId: null
    };
  },
  watch: {
    // 监听变换属性变化，更新图表
    cadTransform: {
      handler() {
        this.updateEchartsLayers();
      },
      deep: true
    },
  },
  mounted() {
    // 使用setTimeout确保DOM完全渲染后再初始化ECharts
    setTimeout(() => {
      this.initEcharts();
    }, 100);

    // 监听窗口大小变化，调整图表大小
    window.addEventListener('resize', this.resizeEcharts);
  },
  beforeDestroy() {
    // 移除事件监听和销毁图表实例
    window.removeEventListener('resize', this.resizeEcharts);

    // 清理鼠标事件监听
    this.cleanupMouseEvents();

    if (this.echartsInstance) {
      this.echartsInstance.dispose();
    }
  },
  methods: {
    // 初始化ECharts实例
    initEcharts() {
      // 确保DOM已经渲染
      this.$nextTick(() => {
        try {
          // 如果已经有实例，先销毁
          if (this.echartsInstance) {
            this.echartsInstance.dispose();
            this.echartsInstance = null;
          }

          // 检查容器是否存在
          if (!this.$refs.echartsContainer) {
            return;
          }

          // 初始化ECharts实例
          this.echartsInstance = echarts.init(this.$refs.echartsContainer);

          // 设置鼠标事件监听
          this.setupMouseEvents();

          // 预加载图片以获取尺寸
          this.preloadImages().then(() => {
            // 设置图表选项
            this.updateEchartsLayers();

            // 监听图表事件
            this.echartsInstance.on('click', this.handleChartClick);
          });
        } catch (error) {
          console.error('初始化ECharts实例时出错:', error);
        }
      });
    },

    // 预加载图片以获取尺寸
    preloadImages() {
      const promises = [];

      // 预加载CAD图层
      if (this.cadLayerUrl) {
        const cadPromise = new Promise((resolve) => {
          const img = new Image();
          img.onload = () => {
            // 保存图片像素尺寸
            this.cadWidthPx = img.width;
            this.cadHeightPx = img.height;

            // 获取后端提供的pt单位尺寸
            const cadWidthPt = this.currentMap && this.currentMap.cadWidth;
            const cadHeightPt = this.currentMap && this.currentMap.cadHeight;

            if (cadWidthPt && cadHeightPt) {
              // 如果后端提供了pt单位尺寸，计算pt到px的转换比例
              this.pt2px = Math.max(
                this.cadWidthPx / cadWidthPt,
                this.cadHeightPx / cadHeightPt
              );

              // 保存pt单位尺寸
              this.cadWidth = cadWidthPt;
              this.cadHeight = cadHeightPt;
            } else {
              // 如果后端没有提供pt单位尺寸，使用标准转换比例
              this.pt2px = 96 / 72; // 标准转换比例：1pt = 1/72英寸，1px = 1/96英寸

              // 从像素尺寸转换到pt
              this.cadWidth = this.cadWidthPx / this.pt2px;
              this.cadHeight = this.cadHeightPx / this.pt2px;
            }

            resolve();
          };
          img.onerror = () => {
            console.error('CAD图层加载失败');
            resolve();
          };
          img.src = this.cadLayerUrl;
        });
        promises.push(cadPromise);
      }

      return Promise.all(promises);
    },

    // 更新ECharts图层
    updateEchartsLayers() {
      if (!this.echartsInstance) return;

      // 获取容器尺寸
      const containerWidth = this.$refs.echartsContainer.clientWidth;
      const containerHeight = this.$refs.echartsContainer.clientHeight;

      // 计算CAD图层的缩放比例，使其最大限度填充容器
      let cadScale = 1;
      if (this.cadWidthPx && this.cadHeightPx) {
        const scaleX = containerWidth / this.cadWidthPx;
        const scaleY = containerHeight / this.cadHeightPx;
        cadScale = Math.min(scaleX, scaleY); // 取较小值，确保完全显示
      }

      // 基础配置
      const option = {
        backgroundColor: '#f5f7fa',
        animation: false,
        hoverLayerThreshold: Infinity,
        grid: {
          show: false,
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          containLabel: false
        },
        xAxis: {
          show: false,
          type: 'value',
          min: 0,
          max: containerWidth
        },
        yAxis: {
          show: false,
          type: 'value',
          min: 0,
          max: containerHeight
        },
        series: [],
        graphic: []
      };

      // 添加CAD图层
      if (this.cadLayerUrl) {
        // 计算居中位置（px）
        const centeredX = (containerWidth - this.cadWidthPx * cadScale) / 2;
        const centeredY = (containerHeight - this.cadHeightPx * cadScale) / 2;

        // 使用cadScale作为基础缩放，cadTransform.scale作为用户调整的缩放，viewState.scale作为交互缩放
        const finalScale = cadScale * this.cadTransform.scale * this.viewState.scale;

        // 计算最终位置，考虑基础位置、用户调整的位置和交互位置
        const finalX = centeredX + this.cadTransform.translateX + this.viewState.translateX;
        const finalY = centeredY + this.cadTransform.translateY + this.viewState.translateY;

        option.graphic.push({
          type: 'image',
          id: 'cadLayer',
          style: {
            image: this.cadLayerUrl,
            opacity: 1,
            width: this.cadWidthPx,
            height: this.cadHeightPx
          },
          position: [finalX, finalY],
          rotation: this.cadTransform.rotation * Math.PI / 180,
          scale: [finalScale, finalScale],
          origin: [0, 0],
          z: 1,
          draggable: false
        });

        // 保存基础缩放和位置，供点位使用
        this.baseScale = cadScale;
        this.baseX = centeredX;
        this.baseY = centeredY;
      }

      // 添加工作点位和路径
      this.addWorkPoints(option, containerWidth, containerHeight);
      this.addRoutes(option, containerWidth, containerHeight);

      // 设置图表选项
      this.echartsInstance.setOption(option, true);
    },

    // 添加工作点位
    addWorkPoints(option, containerWidth, containerHeight) {
      // 添加工作点位
      if (this.currentMap && this.currentMap.advancedPoints && this.currentMap.advancedPoints.length > 0) {
        // 创建散点图系列用于显示点位
        const pointData = [];

        // 使用CAD图层的坐标系，同时考虑交互状态
        const activeScale = this.baseScale * this.cadTransform.scale * this.viewState.scale;
        const activeTranslateX = this.baseX + this.cadTransform.translateX + this.viewState.translateX;
        const activeTranslateY = this.baseY + this.cadTransform.translateY + this.viewState.translateY;

        // 处理每个点位
        this.currentMap.advancedPoints.forEach((point, index) => {
          // 1. 首先将pt单位转换为px单位
          const pointXPx = point.newX * this.pt2px;
          const pointYPx = point.newY * this.pt2px;

          // 2. 然后考虑CAD图层的缩放
          const scaledX = pointXPx * activeScale;
          const scaledY = pointYPx * activeScale;

          // 3. 最后加上CAD图层的偏移量
          const x = activeTranslateX + scaledX;
          const y = activeTranslateY + scaledY;

          // 4. 因为echart是左下角原点，所以需要Y轴翻转
          const finalY = containerHeight - y;
          const finalX = x;

          const instanceName = point.instanceName || `点位${index + 1}`;

          pointData.push([
            finalX,
            finalY,
            `${instanceName}`,
            point.id,
            0, // 用于标记是否高亮
            // point.className || 'LandMark' // 点位类型
          ]);
        });

        // 添加散点图系列
        option.series.push({
          type: 'scatter',
          id: 'workPoints',
          data: pointData,
          symbolSize: (data) => {
            // 高亮点位显示更大
            return data[4] === 1 ? 20 : 10;
          },
          symbol: (data) => {
            // 根据点位类型显示不同的图标
            const type = data[5];
            if (type === 'MaintenancePoint' || type === 'JobPoint') {
              return 'pin'; // 检修点使用图钉形状
            }
            return 'circle'; // 其他点位使用圆形
          },
          itemStyle: {
            color: (params) => {
              // 根据点位类型和高亮状态显示不同的颜色
              const type = params.data[5];
              const isHighlighted = params.data[4] === 1;

              if (isHighlighted) {
                return '#FF9800'; // 高亮点位使用橙色
              }

              if (type === 'MaintenancePoint' || type === 'JobPoint') {
                return '#FF5722'; // 检修点使用红色
              }
              return '#2196F3'; // 其他点位使用蓝色
            },
            borderColor: '#FFFFFF',
            borderWidth: 2,
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          },
          label: {
            show: true,
            position: 'top',
            distance: 5,
            formatter: '{@[2]}', // 显示点位名称
            backgroundColor: '#333',
            padding: [3, 5],
            borderRadius: 3,
            color: '#fff'
          },
          zlevel: 10 // 确保点位显示在最上层
        });
      }
    },

    // 添加路径
    addRoutes(option, containerWidth, containerHeight) {
      if (this.currentMap && this.currentMap.advancedRoutes && this.currentMap.advancedRoutes.length > 0) {
        // 使用CAD图层的坐标系，同时考虑交互状态
        const activeScale = this.baseScale * this.cadTransform.scale * this.viewState.scale;
        const activeTranslateX = this.baseX + this.cadTransform.translateX + this.viewState.translateX;
        const activeTranslateY = this.baseY + this.cadTransform.translateY + this.viewState.translateY;

        // 遍历所有路径
        this.currentMap.advancedRoutes.forEach((route, index) => {
          // 1. 首先将pt单位转换为px单位
          const startXPx = route.startPos.newX * this.pt2px;
          const startYPx = route.startPos.newY * this.pt2px;
          const endXPx = route.endPos.newX * this.pt2px;
          const endYPx = route.endPos.newY * this.pt2px;

          // 控制点只在需要时计算
          let ctrlX1Px, ctrlY1Px, ctrlX2Px, ctrlY2Px;
          if (route.ctrlPos1 && route.ctrlPos2) {
            ctrlX1Px = route.ctrlPos1.newX * this.pt2px;
            ctrlY1Px = route.ctrlPos1.newY * this.pt2px;
            ctrlX2Px = route.ctrlPos2.newX * this.pt2px;
            ctrlY2Px = route.ctrlPos2.newY * this.pt2px;
          }

          // 2. 计算最终坐标
          const startX = activeTranslateX + startXPx * activeScale;
          const startY = containerHeight - (activeTranslateY + startYPx * activeScale);
          const endX = activeTranslateX + endXPx * activeScale;
          const endY = containerHeight - (activeTranslateY + endYPx * activeScale);

          // 控制点坐标只在需要时计算
          let ctrlX1, ctrlY1, ctrlX2, ctrlY2;
          if (ctrlX1Px !== undefined) {
            ctrlX1 = activeTranslateX + ctrlX1Px * activeScale;
            ctrlY1 = containerHeight - (activeTranslateY + ctrlY1Px * activeScale);
            ctrlX2 = activeTranslateX + ctrlX2Px * activeScale;
            ctrlY2 = containerHeight - (activeTranslateY + ctrlY2Px * activeScale);
          }

          // 3. 根据路径类型绘制不同的路径
          let graphicItem = null;

          switch (route.type) {
            case 'straight_line': // 直线
              graphicItem = {
                type: 'line',
                coordinateSystem: 'cartesian2d',
                data: [[startX, startY], [endX, endY]],
                lineStyle: {
                  width: 3,
                  color: '#4CAF50'
                },
                z: 5
              };
              option.series.push(graphicItem);
              break;

            case 'bezier_curve': // 贝塞尔曲线
              const bezierPoints = calculateBezierPoints(
                [startX, startY],
                [ctrlX1, ctrlY1],
                [ctrlX2, ctrlY2],
                [endX, endY],
                100 // 生成100个点以获得平滑的曲线
              );

              // 使用ECharts标准的贝塞尔曲线实现
              graphicItem = {
                type: 'line',
                coordinateSystem: 'cartesian2d',
                symbolSize: 1,
                symbol: 'bezierCurve',
                smooth: true, // 使用平滑曲线
                data: bezierPoints,
                smoothMonotone: 'none', // 允许曲线自由弯曲
                lineStyle: {
                  width: 3,
                  color: '#2196F3',
                  opacity: 0.8
                },
                z: 5
              };

              option.series.push(graphicItem);
              break;

            case 'convex': // 凸弧线
            case 'concave_arc': // 凹弧线
              const p1 = [startX, startY];
              const p2 = [endX, endY];
              // 生成弧线点
              const arcPoints = calculateArcPoints(p1, p2, route.radian, true);

              graphicItem = {
                type: 'lines',
                coordinateSystem: 'cartesian2d',
                polyline: true,
                data: [{
                  coords: arcPoints,
                  lineStyle: {
                    width: 3,
                    color: '#4CAF50'
                  }
                }],
                effect: { show: false },
                lineStyle: {
                  opacity: 0.8,
                  width: 3,
                  color: '#4CAF50'
                },
                z: 5
              };

              option.series.push(graphicItem);
              break;

            default:
              console.warn(`未知的路径类型: ${route.type}`);
              break;
          }
        });
      }
    },

    // 调整图表大小
    resizeEcharts() {
      if (this.echartsInstance) {
        this.echartsInstance.resize();
        this.updateEchartsLayers();
      }
    },

    // 处理图表点击事件
    handleChartClick(params) {
      if (this.addingPoint) {
        // 添加新点位
        this.addNewPoint(params.event.offsetX, params.event.offsetY);
        return;
      }

      // 点击点位
      if (params.componentSubType === 'scatter' && params.data && params.data.length > 3) {
        const pointId = params.data[3];
        const point = this.currentMap.advancedPoints.find(p => p.id === pointId);
        if (point) {
          this.highlightedPointId = pointId;
          this.updateEchartsLayers(); // 更新高亮显示
          this.$emit('point-click', point);
        }
      }
    },

    // 设置鼠标事件监听
    setupMouseEvents() {
      if (!this.echartsInstance || !this.$refs.echartsContainer) return;

      const container = this.$refs.echartsContainer;

      // 鼠标滚轮事件 - 缩放
      container.addEventListener('wheel', this.handleMouseWheel);

      // 鼠标按下事件 - 开始拖动
      container.addEventListener('mousedown', this.handleMouseDown);

      // 鼠标移动事件 - 拖动中
      container.addEventListener('mousemove', this.handleMouseMove);

      // 鼠标松开事件 - 结束拖动
      container.addEventListener('mouseup', this.handleMouseUp);
      container.addEventListener('mouseleave', this.handleMouseUp);

      // 设置初始鼠标样式
      container.style.cursor = 'grab';
    },

    // 清理鼠标事件监听
    cleanupMouseEvents() {
      if (!this.$refs.echartsContainer) return;

      const container = this.$refs.echartsContainer;

      container.removeEventListener('wheel', this.handleMouseWheel);
      container.removeEventListener('mousedown', this.handleMouseDown);
      container.removeEventListener('mousemove', this.handleMouseMove);
      container.removeEventListener('mouseup', this.handleMouseUp);
      container.removeEventListener('mouseleave', this.handleMouseUp);
    },

    // 处理鼠标滚轮事件 - 缩放
    handleMouseWheel(event) {
      event.preventDefault();

      // 获取鼠标位置相对于容器的坐标
      const rect = this.$refs.echartsContainer.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      // 计算缩放方向和大小
      const delta = event.deltaY > 0 ? -this.zoomConfig.step : this.zoomConfig.step;
      const newScale = Math.max(
        this.zoomConfig.min,
        Math.min(this.zoomConfig.max, this.viewState.scale + delta)
      );

      // 如果缩放没有变化，不进行操作
      if (newScale === this.viewState.scale) return;

      // 计算缩放中心点相对于图像的位置
      const imageX = mouseX - this.viewState.translateX;
      const imageY = mouseY - this.viewState.translateY;

      // 计算新的平移量，保持鼠标位置不变
      const scaleFactor = newScale / this.viewState.scale;
      const newTranslateX = mouseX - imageX * scaleFactor;
      const newTranslateY = mouseY - imageY * scaleFactor;

      // 更新视图状态
      this.viewState = {
        scale: newScale,
        translateX: newTranslateX,
        translateY: newTranslateY
      };

      // 更新图层显示
      this.updateEchartsLayers();
    },

    // 处理鼠标按下事件 - 开始拖动
    handleMouseDown(event) {
      // 只响应左键
      if (event.button !== 0) return;

      this.isDragging = true;
      this.lastMouseX = event.clientX;
      this.lastMouseY = event.clientY;

      // 改变鼠标样式
      this.$refs.echartsContainer.style.cursor = 'grabbing';
    },

    // 处理鼠标移动事件 - 拖动中
    handleMouseMove(event) {
      if (!this.isDragging) return;

      // 计算鼠标移动距离
      const deltaX = event.clientX - this.lastMouseX;
      const deltaY = event.clientY - this.lastMouseY;

      // 更新鼠标位置
      this.lastMouseX = event.clientX;
      this.lastMouseY = event.clientY;

      // 更新视图状态
      this.viewState.translateX += deltaX;
      this.viewState.translateY += deltaY;

      // 更新图层显示
      this.updateEchartsLayers();
    },

    // 处理鼠标松开事件 - 结束拖动
    handleMouseUp() {
      if (!this.isDragging) return;

      this.isDragging = false;

      // 恢复鼠标样式
      this.$refs.echartsContainer.style.cursor = this.addingPoint ? 'crosshair' : 'grab';
    },

    // 更新地图元素
    updateMapElements() {
      // 检查是否有当前地图
      if (!this.currentMap) {
        return;
      }

      // 检查是否有CAD图层URL
      if (!this.cadLayerUrl) {
        return;
      }

      // 重新渲染地图
      this.$nextTick(() => {
        // 确保容器已经渲染
        if (!this.$refs.echartsContainer) {
          return;
        }

        if (this.echartsInstance) {
          this.updateEchartsLayers();
        } else {
          this.initEcharts();
        }
      });
    },

    // 切换添加点位模式
    toggleAddingPoint() {
      this.addingPoint = !this.addingPoint;
      if (this.$refs.echartsContainer) {
        this.$refs.echartsContainer.style.cursor = this.addingPoint ? 'crosshair' : 'grab';
      }
    },

    // 添加新点位
    addNewPoint(clientX, clientY) {
      if (!this.currentMap || !this.cadLayerUrl) return;

      // 获取容器尺寸
      const containerWidth = this.$refs.echartsContainer.clientWidth;
      const containerHeight = this.$refs.echartsContainer.clientHeight;

      // 使用CAD图层的坐标系，同时考虑交互状态
      const activeScale = this.baseScale * this.cadTransform.scale * this.viewState.scale;
      const activeTranslateX = this.baseX + this.cadTransform.translateX + this.viewState.translateX;
      const activeTranslateY = this.baseY + this.cadTransform.translateY + this.viewState.translateY;

      // 反向计算pt单位坐标
      // 1. 首先将客户端坐标转换为相对于容器的坐标
      const x = clientX;
      const y = containerHeight - clientY; // Y轴翻转

      // 2. 减去CAD图层的偏移量
      const relativeX = x - activeTranslateX;
      const relativeY = y - activeTranslateY;

      // 3. 除以缩放比例
      const unscaledX = relativeX / activeScale;
      const unscaledY = relativeY / activeScale;

      // 4. 转换为pt单位
      const ptX = unscaledX / this.pt2px;
      const ptY = unscaledY / this.pt2px;

      // 创建新点位
      const newPoint = {
        id: Date.now(), // 临时ID
        x: ptX,
        y: ptY,
        name: `检修点${this.currentMap.advancedPoints.length + 1}`,
        instanceName: `检修点${this.currentMap.advancedPoints.length + 1}`,
        className: 'MaintenancePoint',
        allowRevolve: '1'
      };

      // 发送添加点位事件
      this.$emit('add-point', newPoint);

      // 关闭添加点位模式
      this.addingPoint = false;
      if (this.$refs.echartsContainer) {
        this.$refs.echartsContainer.style.cursor = 'grab';
      }

      // 更新地图
      this.updateMapElements();
    },

    // 高亮显示点位
    highlightPoint(point) {
      if (!point) {
        this.highlightedPointId = null;
      } else {
        this.highlightedPointId = point.id;
      }
      this.updateEchartsLayers();
    },

    // 重置视图
    resetView() {
      this.viewState = {
        scale: 1,
        translateX: 0,
        translateY: 0
      };
      this.updateEchartsLayers();
    }
  }
};
</script>

<style lang="scss" scoped>
.maintenance-map-panel {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .map-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #f5f7fa;
    border-radius: 4px;

    .placeholder-map {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
    }

    .map-content {
      height: 100%;
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .echarts-container {
        height: 100%;
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        cursor: grab; /* 默认显示抓取手势 */

        &:active {
          cursor: grabbing; /* 激活时显示抓取中手势 */
        }
      }
    }
  }

  .map-toolbar {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 5px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .el-tooltip {
      display: block;
      margin-bottom: 5px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .el-button.is-active {
      color: #fff;
      background-color: #409EFF;
      border-color: #409EFF;
    }
  }
}
</style>
